#!/bin/bash
# SQLite Veritabanı Yöneticisi

DB_PATH="/Users/<USER>/Library/Application Support/telefoncu-takip-electron/telefoncu-takip.db"

echo "🗄️  SQLite Veritabanı Yöneticisi"
echo "================================"
echo "📁 Veritabanı: $DB_PATH"
echo ""

if [ ! -f "$DB_PATH" ]; then
    echo "❌ Veritabanı bulunamadı!"
    echo "💡 Önce uygulamayı çalıştırın: npm run electron"
    exit 1
fi

echo "📋 Tablolar:"
sqlite3 "$DB_PATH" ".tables"
echo ""

echo "📊 İstatistikler:"
echo -n "Ürün<PERSON>: "
sqlite3 "$DB_PATH" "SELECT COUNT(*) FROM urunler;"
echo -n "İşlemler: "
sqlite3 "$DB_PATH" "SELECT COUNT(*) FROM islemler;"
echo ""

echo "🐚 SQLite shell başlatılıyor..."
echo "💡 Çıkmak için .quit yazın"
sqlite3 "$DB_PATH"