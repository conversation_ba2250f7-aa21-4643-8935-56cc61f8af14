#!/usr/bin/env node

// Cross-platform SQLite Veritabanı Yöneticisi
const path = require('path');
const os = require('os');
const fs = require('fs');
const { spawn, exec } = require('child_process');

// Platform-agnostic veritabanı yolu
function getDatabasePath() {
  let userDataPath;

  switch (process.platform) {
    case 'darwin': // macOS
      userDataPath = path.join(os.homedir(), 'Library', 'Application Support', 'telefoncu-takip-electron');
      break;
    case 'win32': // Windows
      userDataPath = path.join(os.homedir(), 'AppData', 'Roaming', 'telefoncu-takip-electron');
      break;
    case 'linux': // Linux
      userDataPath = path.join(os.homedir(), '.config', 'telefoncu-takip-electron');
      break;
    default:
      userDataPath = path.join(os.homedir(), '.telefoncu-takip-electron');
  }

  return path.join(userDataPath, 'telefoncu-takip.db');
}

// SQLite komut yolunu belirle
function getSQLiteCommand() {
  const isWindows = process.platform === 'win32';

  if (isWindows) {
    // Windows için SQLite binary'lerini kontrol et
    const possiblePaths = [
      'sqlite3.exe',
      path.join(process.cwd(), 'bin', 'sqlite3.exe'),
      path.join(__dirname, '..', 'bin', 'sqlite3.exe'),
      'C:\\sqlite\\sqlite3.exe',
      'C:\\Program Files\\SQLite\\sqlite3.exe'
    ];

    for (const sqlitePath of possiblePaths) {
      try {
        if (fs.existsSync(sqlitePath) || sqlitePath === 'sqlite3.exe') {
          return sqlitePath;
        }
      } catch (error) {
        continue;
      }
    }

    return 'sqlite3.exe'; // Varsayılan
  } else {
    return 'sqlite3';
  }
}

// Veritabanı durumunu kontrol et
function checkDatabase() {
  const dbPath = getDatabasePath();
  const exists = fs.existsSync(dbPath);

  console.log('🗄️  SQLite Veritabanı Durumu');
  console.log('=' .repeat(40));
  console.log(`📊 Platform: ${process.platform} (${os.arch()})`);
  console.log(`🏠 Kullanıcı: ${os.userInfo().username}`);
  console.log(`📁 Veritabanı: ${dbPath}`);
  console.log(`✅ Durum: ${exists ? 'Mevcut' : 'Bulunamadı'}`);

  if (exists) {
    const stats = fs.statSync(dbPath);
    console.log(`📏 Boyut: ${(stats.size / 1024).toFixed(2)} KB`);
    console.log(`📅 Değişiklik: ${stats.mtime.toLocaleString()}`);
  } else {
    console.log('⚠️  Veritabanı bulunamadı! Önce uygulamayı çalıştırın.');
  }

  return { dbPath, exists };
}

// SQLite komutunu çalıştır
function runSQLiteCommand(dbPath, query = null) {
  const sqliteCmd = getSQLiteCommand();

  console.log(`\n🔧 SQLite Komutu: ${sqliteCmd}`);

  if (query) {
    console.log(`📝 Sorgu: ${query}`);
    console.log('-'.repeat(50));

    const args = [dbPath, query];
    const child = spawn(sqliteCmd, args, { stdio: 'inherit' });

    child.on('error', (error) => {
      console.error(`❌ Hata: ${error.message}`);
      if (error.code === 'ENOENT') {
        console.log('\n💡 SQLite kurulu değil. Kurulum için:');
        if (process.platform === 'win32') {
          console.log('   - https://sqlite.org/download.html adresinden indirin');
          console.log('   - sqlite3.exe dosyasını PATH\'e ekleyin');
        } else {
          console.log('   - macOS: brew install sqlite');
          console.log('   - Ubuntu: sudo apt install sqlite3');
        }
      }
    });

    child.on('close', (code) => {
      if (code !== 0) {
        console.log(`\n⚠️  İşlem kodu: ${code}`);
      }
    });
  } else {
    // Interactive shell
    console.log('🐚 SQLite shell başlatılıyor...');
    console.log('💡 Çıkmak için .quit yazın\n');

    const child = spawn(sqliteCmd, [dbPath], { stdio: 'inherit' });

    child.on('error', (error) => {
      console.error(`❌ Hata: ${error.message}`);
    });
  }
}

// Hızlı istatistikler
function showQuickStats(dbPath) {
  const sqliteCmd = getSQLiteCommand();

  console.log('\n📊 Hızlı İstatistikler:');
  console.log('-'.repeat(30));

  const queries = [
    { name: 'Tablolar', query: '.tables' },
    { name: 'Ürün Sayısı', query: 'SELECT COUNT(*) as count FROM urunler;' },
    { name: 'İşlem Sayısı', query: 'SELECT COUNT(*) as count FROM islemler;' },
    { name: 'Müşteri Sayısı', query: 'SELECT COUNT(*) as count FROM musteriler;' }
  ];

  queries.forEach((item, index) => {
    setTimeout(() => {
      console.log(`\n${item.name}:`);
      exec(`${sqliteCmd} "${dbPath}" "${item.query}"`, (error, stdout, stderr) => {
        if (error) {
          console.log(`❌ Hata: ${error.message}`);
        } else if (stderr) {
          console.log(`⚠️  Uyarı: ${stderr}`);
        } else {
          console.log(stdout.trim() || 'Sonuç yok');
        }
      });
    }, index * 500);
  });
}

// Yedekleme
function backupDatabase(dbPath) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
  const backupPath = dbPath.replace('.db', `_backup_${timestamp}.db`);

  try {
    fs.copyFileSync(dbPath, backupPath);
    console.log(`✅ Yedek oluşturuldu: ${backupPath}`);

    const stats = fs.statSync(backupPath);
    console.log(`📏 Yedek boyutu: ${(stats.size / 1024).toFixed(2)} KB`);
  } catch (error) {
    console.error(`❌ Yedekleme hatası: ${error.message}`);
  }
}

// Platform-specific scriptler oluştur
function createPlatformScripts(dbPath) {
  const scriptsDir = path.dirname(__filename);
  const sqliteCmd = getSQLiteCommand();

  if (process.platform === 'win32') {
    // Windows Batch
    const batchContent = `@echo off
chcp 65001 >nul
title SQLite Veritabanı Yöneticisi
echo.
echo 🗄️  SQLite Veritabanı Yöneticisi - Windows
echo ========================================
echo.
echo 📁 Veritabanı: "${dbPath}"
echo.

if not exist "${dbPath}" (
    echo ❌ HATA: Veritabanı dosyası bulunamadı!
    echo 💡 Önce Electron uygulamasını çalıştırın: npm run electron
    echo.
    pause
    exit /b 1
)

echo ✅ Veritabanı bulundu
echo.

:menu
echo 🔧 Seçenekler:
echo 1. Tabloları listele
echo 2. Ürün sayısını göster
echo 3. İşlem sayısını göster
echo 4. SQLite shell aç
echo 5. Yedek oluştur
echo 6. Çıkış
echo.
set /p choice="Seçiminiz (1-6): "

if "%choice%"=="1" (
    echo.
    echo 📋 Tablolar:
    ${sqliteCmd} "${dbPath}" ".tables"
    echo.
    goto menu
)
if "%choice%"=="2" (
    echo.
    echo 📦 Ürün Sayısı:
    ${sqliteCmd} "${dbPath}" "SELECT COUNT(*) as urun_sayisi FROM urunler;"
    echo.
    goto menu
)
if "%choice%"=="3" (
    echo.
    echo 💰 İşlem Sayısı:
    ${sqliteCmd} "${dbPath}" "SELECT COUNT(*) as islem_sayisi FROM islemler;"
    echo.
    goto menu
)
if "%choice%"=="4" (
    echo.
    echo 🐚 SQLite shell başlatılıyor...
    echo 💡 Çıkmak için .quit yazın
    echo.
    ${sqliteCmd} "${dbPath}"
    echo.
    goto menu
)
if "%choice%"=="5" (
    echo.
    echo 💾 Yedek oluşturuluyor...
    copy "${dbPath}" "${dbPath.replace('.db', '_backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%.db')}"
    echo ✅ Yedek oluşturuldu
    echo.
    goto menu
)
if "%choice%"=="6" (
    echo.
    echo 👋 Çıkılıyor...
    exit /b 0
)

echo ❌ Geçersiz seçim!
echo.
goto menu
`;

    const batchPath = path.join(scriptsDir, 'db-manager.bat');
    fs.writeFileSync(batchPath, batchContent);
    console.log(`✅ Windows batch script: ${batchPath}`);
  } else {
    // Unix/Linux/macOS Shell - Basit versiyon
    const shellLines = [
      '#!/bin/bash',
      '# SQLite Veritabanı Yöneticisi',
      '',
      'DB_PATH="' + dbPath + '"',
      '',
      'echo "🗄️  SQLite Veritabanı Yöneticisi"',
      'echo "================================"',
      'echo "📁 Veritabanı: $DB_PATH"',
      'echo ""',
      '',
      'if [ ! -f "$DB_PATH" ]; then',
      '    echo "❌ Veritabanı bulunamadı!"',
      '    echo "💡 Önce uygulamayı çalıştırın: npm run electron"',
      '    exit 1',
      'fi',
      '',
      'echo "📋 Tablolar:"',
      'sqlite3 "$DB_PATH" ".tables"',
      'echo ""',
      '',
      'echo "📊 İstatistikler:"',
      'echo -n "Ürünler: "',
      'sqlite3 "$DB_PATH" "SELECT COUNT(*) FROM urunler;"',
      'echo -n "İşlemler: "',
      'sqlite3 "$DB_PATH" "SELECT COUNT(*) FROM islemler;"',
      'echo ""',
      '',
      'echo "🐚 SQLite shell başlatılıyor..."',
      'echo "💡 Çıkmak için .quit yazın"',
      'sqlite3 "$DB_PATH"'
    ];

    const shellContent = shellLines.join('\n');
    const shellPath = path.join(scriptsDir, 'db-manager.sh');
    fs.writeFileSync(shellPath, shellContent);
    fs.chmodSync(shellPath, '755');
    console.log(`✅ Shell script: ${shellPath}`);
  }
}

// Ana fonksiyon
function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  const { dbPath, exists } = checkDatabase();

  if (!exists && command !== 'info') {
    console.log('\n💡 Veritabanı oluşturmak için önce uygulamayı çalıştırın:');
    console.log('   npm run electron');
    return;
  }

  switch (command) {
    case 'info':
      // Sadece bilgi göster
      break;

    case 'stats':
      showQuickStats(dbPath);
      break;

    case 'shell':
      runSQLiteCommand(dbPath);
      break;

    case 'query':
      const query = args[1];
      if (query) {
        runSQLiteCommand(dbPath, query);
      } else {
        console.log('\n❌ Sorgu belirtilmedi!');
        console.log('💡 Kullanım: node db-manager.js query "SELECT * FROM urunler"');
      }
      break;

    case 'backup':
      backupDatabase(dbPath);
      break;

    case 'scripts':
      createPlatformScripts(dbPath);
      break;

    default:
      console.log('\n🔧 Kullanılabilir Komutlar:');
      console.log('  node db-manager.js info     - Veritabanı bilgileri');
      console.log('  node db-manager.js stats    - Hızlı istatistikler');
      console.log('  node db-manager.js shell    - SQLite shell aç');
      console.log('  node db-manager.js query "SQL" - Sorgu çalıştır');
      console.log('  node db-manager.js backup   - Yedek oluştur');
      console.log('  node db-manager.js scripts  - Platform scriptleri oluştur');

      if (exists) {
        console.log('\n📊 Hızlı Bilgiler:');
        showQuickStats(dbPath);
      }
  }
}

// Export
module.exports = { getDatabasePath, checkDatabase, runSQLiteCommand };

// Scripti çalıştır
if (require.main === module) {
  main();
}
