directories:
  output: dist
  buildResources: build
appId: com.telefoncu.takip
productName: Telefoncu Takip Sistemi
files:
  - filter:
      - out/**/*
      - electron/**/*
      - scripts/**/*
      - node_modules/**/*
      - '!node_modules/.cache/**/*'
extraResources:
  - from: scripts/
    to: scripts/
    filter:
      - '**/*'
mac:
  category: public.app-category.business
  identity: null
win:
  target: nsis
  requestedExecutionLevel: asInvoker
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: Telefoncu Takip Sistemi
linux:
  target: AppImage
electronVersion: 32.3.3
