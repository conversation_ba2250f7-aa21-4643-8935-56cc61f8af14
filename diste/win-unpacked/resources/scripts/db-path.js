#!/usr/bin/env node

// Platform-agnostic veritabanı yolu belirleme
const path = require('path');
const os = require('os');
const fs = require('fs');

// Veritabanı yolunu platform bazında belirle
function getDatabasePath() {
  let userDataPath;
  
  switch (process.platform) {
    case 'darwin': // macOS
      userDataPath = path.join(os.homedir(), 'Library', 'Application Support', 'telefoncu-takip-electron');
      break;
    case 'win32': // Windows
      userDataPath = path.join(os.homedir(), 'AppData', 'Roaming', 'telefoncu-takip-electron');
      break;
    case 'linux': // Linux
      userDataPath = path.join(os.homedir(), '.config', 'telefoncu-takip-electron');
      break;
    default:
      userDataPath = path.join(os.homedir(), '.telefoncu-takip-electron');
  }
  
  return path.join(userDataPath, 'telefoncu-takip.db');
}

// Platform bilgilerini göster
function showPlatformInfo() {
  const dbPath = getDatabasePath();
  
  console.log('🖥️  Platform Bilgileri');
  console.log('=' .repeat(40));
  console.log(`📊 İşletim Sistemi: ${process.platform}`);
  console.log(`🏠 Ana Dizin: ${os.homedir()}`);
  console.log(`📁 Veritabanı Yolu: ${dbPath}`);
  console.log(`✅ Dosya Var: ${fs.existsSync(dbPath) ? 'Evet' : 'Hayır'}`);
  
  if (fs.existsSync(dbPath)) {
    const stats = fs.statSync(dbPath);
    console.log(`📏 Dosya Boyutu: ${(stats.size / 1024).toFixed(2)} KB`);
    console.log(`📅 Son Değişiklik: ${stats.mtime.toLocaleString()}`);
  }
  
  return dbPath;
}

// Platform-specific komutları oluştur
function generateCommands() {
  const dbPath = getDatabasePath();
  const isWindows = process.platform === 'win32';
  
  console.log('\n🔧 Platform-Specific Komutlar:');
  console.log('-'.repeat(50));
  
  if (isWindows) {
    console.log('📋 Windows Komutları:');
    console.log(`sqlite3.exe "${dbPath}"`);
    console.log(`sqlite3.exe "${dbPath}" ".tables"`);
    console.log(`sqlite3.exe "${dbPath}" ".schema"`);
    console.log(`sqlite3.exe "${dbPath}" "SELECT COUNT(*) FROM urunler;"`);
    
    console.log('\n📋 PowerShell Komutları:');
    console.log(`& sqlite3.exe "${dbPath}" ".tables"`);
    console.log(`& sqlite3.exe "${dbPath}" ".schema"`);
    
    console.log('\n📋 CMD Komutları:');
    console.log(`sqlite3.exe "${dbPath}" .tables`);
    console.log(`sqlite3.exe "${dbPath}" .schema`);
  } else {
    console.log('📋 Unix/Linux/macOS Komutları:');
    console.log(`sqlite3 "${dbPath}"`);
    console.log(`sqlite3 "${dbPath}" ".tables"`);
    console.log(`sqlite3 "${dbPath}" ".schema"`);
    console.log(`sqlite3 "${dbPath}" "SELECT COUNT(*) FROM urunler;"`);
  }
  
  console.log('\n📋 Cross-Platform Node.js:');
  console.log(`node -e "console.log('${dbPath}')"`);
  console.log(`npm run db:info`);
  console.log(`npm run db:stats`);
}

// Batch/Shell script oluştur
function createPlatformScripts() {
  const dbPath = getDatabasePath();
  const scriptsDir = path.join(__dirname);
  
  if (process.platform === 'win32') {
    // Windows Batch Script
    const batchContent = `@echo off
echo SQLite Veritabanı Bağlantı Scripti - Windows
echo =============================================
echo.
echo Veritabanı Yolu: "${dbPath}"
echo.
if not exist "${dbPath}" (
    echo HATA: Veritabanı dosyası bulunamadı!
    echo Önce Electron uygulamasını çalıştırın: npm run electron
    pause
    exit /b 1
)

echo Mevcut tablolar:
sqlite3.exe "${dbPath}" ".tables"
echo.

echo Ürün sayısı:
sqlite3.exe "${dbPath}" "SELECT COUNT(*) as urun_sayisi FROM urunler;"
echo.

echo İşlem sayısı:
sqlite3.exe "${dbPath}" "SELECT COUNT(*) as islem_sayisi FROM islemler;"
echo.

echo SQLite shell başlatılıyor...
echo Çıkmak için .quit yazın
sqlite3.exe "${dbPath}"
`;
    
    const batchPath = path.join(scriptsDir, 'db-connect.bat');
    fs.writeFileSync(batchPath, batchContent);
    console.log(`✅ Windows batch script oluşturuldu: ${batchPath}`);
    
    // PowerShell Script
    const psContent = `# SQLite Veritabanı Bağlantı Scripti - PowerShell
Write-Host "SQLite Veritabanı Bağlantı Scripti - Windows PowerShell" -ForegroundColor Green
Write-Host "=======================================================" -ForegroundColor Green
Write-Host ""
Write-Host "Veritabanı Yolu: ${dbPath}" -ForegroundColor Yellow
Write-Host ""

if (-not (Test-Path "${dbPath}")) {
    Write-Host "HATA: Veritabanı dosyası bulunamadı!" -ForegroundColor Red
    Write-Host "Önce Electron uygulamasını çalıştırın: npm run electron" -ForegroundColor Yellow
    Read-Host "Devam etmek için Enter'a basın"
    exit 1
}

Write-Host "Mevcut tablolar:" -ForegroundColor Cyan
& sqlite3.exe "${dbPath}" ".tables"
Write-Host ""

Write-Host "Ürün sayısı:" -ForegroundColor Cyan
& sqlite3.exe "${dbPath}" "SELECT COUNT(*) as urun_sayisi FROM urunler;"
Write-Host ""

Write-Host "İşlem sayısı:" -ForegroundColor Cyan
& sqlite3.exe "${dbPath}" "SELECT COUNT(*) as islem_sayisi FROM islemler;"
Write-Host ""

Write-Host "SQLite shell başlatılıyor..." -ForegroundColor Green
Write-Host "Çıkmak için .quit yazın" -ForegroundColor Yellow
& sqlite3.exe "${dbPath}"
`;
    
    const psPath = path.join(scriptsDir, 'db-connect.ps1');
    fs.writeFileSync(psPath, psContent);
    console.log(`✅ PowerShell script oluşturuldu: ${psPath}`);
    
  } else {
    // Unix/Linux/macOS Shell Script
    const shellContent = `#!/bin/bash
# SQLite Veritabanı Bağlantı Scripti - Unix/Linux/macOS

echo "SQLite Veritabanı Bağlantı Scripti - Unix/Linux/macOS"
echo "=================================================="
echo ""
echo "Veritabanı Yolu: ${dbPath}"
echo ""

if [ ! -f "${dbPath}" ]; then
    echo "HATA: Veritabanı dosyası bulunamadı!"
    echo "Önce Electron uygulamasını çalıştırın: npm run electron"
    read -p "Devam etmek için Enter'a basın..."
    exit 1
fi

echo "Mevcut tablolar:"
sqlite3 "${dbPath}" ".tables"
echo ""

echo "Ürün sayısı:"
sqlite3 "${dbPath}" "SELECT COUNT(*) as urun_sayisi FROM urunler;"
echo ""

echo "İşlem sayısı:"
sqlite3 "${dbPath}" "SELECT COUNT(*) as islem_sayisi FROM islemler;"
echo ""

echo "SQLite shell başlatılıyor..."
echo "Çıkmak için .quit yazın"
sqlite3 "${dbPath}"
`;
    
    const shellPath = path.join(scriptsDir, 'db-connect.sh');
    fs.writeFileSync(shellPath, shellContent);
    fs.chmodSync(shellPath, '755'); // Executable yap
    console.log(`✅ Shell script oluşturuldu: ${shellPath}`);
  }
}

// Ana fonksiyon
function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  switch (command) {
    case 'path':
      console.log(getDatabasePath());
      break;
      
    case 'info':
      showPlatformInfo();
      break;
      
    case 'commands':
      showPlatformInfo();
      generateCommands();
      break;
      
    case 'scripts':
      showPlatformInfo();
      createPlatformScripts();
      break;
      
    default:
      showPlatformInfo();
      generateCommands();
      console.log('\n🔧 Kullanılabilir Komutlar:');
      console.log('  node db-path.js path      - Sadece veritabanı yolunu göster');
      console.log('  node db-path.js info      - Platform bilgilerini göster');
      console.log('  node db-path.js commands  - Platform-specific komutları göster');
      console.log('  node db-path.js scripts   - Platform scriptleri oluştur');
  }
}

// Export
module.exports = { getDatabasePath, showPlatformInfo, generateCommands };

// Scripti çalıştır
if (require.main === module) {
  main();
}
