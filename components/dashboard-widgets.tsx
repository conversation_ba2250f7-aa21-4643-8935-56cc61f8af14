"use client"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useUrun<PERSON>, useIslemler } from "@/lib/db"
import { Badge } from "@/components/ui/badge"
import { AlertCircle, TrendingUp } from "lucide-react"
import Link from "next/link"

export function StokDurumu() {
  const { urunler } = useUrunler()

  // Kritik stok seviyesi
  const kritikStokSeviyesi = 3
  const kritikStokUrunleri = urunler.filter((urun) => urun.stok <= kritikStokSeviyesi)

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium flex items-center justify-between">
          <span>Stok Durumu</span>
          {kritikStokUrunleri.length > 0 && <Badge variant="destructive">{kritikStokUrunleri.length} Kritik</Badge>}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-muted-foreground">Toplam Ürün</span>
            <span className="font-medium">{urunler.length}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Telefonlar</span>
            <span className="font-medium">{urunler.filter((u) => u.kategori === "Telefon").length}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Aksesuarlar</span>
            <span className="font-medium">{urunler.filter((u) => u.kategori === "Aksesuar").length}</span>
          </div>
          {kritikStokUrunleri.length > 0 && (
            <div className="mt-4 pt-2 border-t">
              <div className="flex items-center text-red-500 mb-2">
                <AlertCircle className="h-4 w-4 mr-1" />
                <span className="text-sm font-medium">Kritik Stok Uyarısı</span>
              </div>
              <div className="text-sm text-muted-foreground">
                {kritikStokUrunleri.slice(0, 2).map((urun, i) => (
                  <div key={i} className="flex justify-between">
                    <span>{urun.ad}</span>
                    <span className="font-medium">{urun.stok} adet</span>
                  </div>
                ))}
                {kritikStokUrunleri.length > 2 && (
                  <div className="text-center mt-1">
                    <Link href="/urunler">
                      <Button variant="link" size="sm" className="h-auto p-0">
                        {kritikStokUrunleri.length - 2} ürün daha göster
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export function SatisOzeti() {
  const { islemler } = useIslemler()

  // Son 7 gün ve 30 günlük satışlar
  const bugun = new Date()
  const yediGunOnce = new Date(bugun)
  yediGunOnce.setDate(bugun.getDate() - 7)

  const otuzGunOnce = new Date(bugun)
  otuzGunOnce.setDate(bugun.getDate() - 30)

  const sonYediGunIslemler = islemler.filter((islem) => islem.tarih >= yediGunOnce)
  const sonOtuzGunIslemler = islemler.filter((islem) => islem.tarih >= otuzGunOnce)

  const haftalikCiro = sonYediGunIslemler.reduce((sum, islem) => sum + islem.satisFiyati, 0)
  const haftalikKar = sonYediGunIslemler.reduce((sum, islem) => sum + islem.kar, 0)

  const aylikCiro = sonOtuzGunIslemler.reduce((sum, islem) => sum + islem.satisFiyati, 0)
  const aylikKar = sonOtuzGunIslemler.reduce((sum, islem) => sum + islem.kar, 0)

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">Satış Özeti</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-muted-foreground">Haftalık Ciro</span>
            <span className="font-medium">₺{haftalikCiro.toLocaleString()}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Haftalık Kar</span>
            <span className="font-medium text-green-600">₺{haftalikKar.toLocaleString()}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Aylık Ciro</span>
            <span className="font-medium">₺{aylikCiro.toLocaleString()}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Aylık Kar</span>
            <span className="font-medium text-green-600">₺{aylikKar.toLocaleString()}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Kar Marjı</span>
            <span className="font-medium">
              {aylikCiro > 0 ? `%${((aylikKar / aylikCiro) * 100).toFixed(2)}` : "%0.00"}
            </span>
          </div>
          <div className="mt-4 pt-2 border-t">
            <Link href="/raporlar">
              <Button variant="outline" size="sm" className="w-full">
                <TrendingUp className="h-4 w-4 mr-2" /> Detaylı Raporlar
              </Button>
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export function SonIslemler() {
  const { islemler } = useIslemler()

  // Son 5 işlem
  const sonIslemler = [...islemler].sort((a, b) => b.tarih.getTime() - a.tarih.getTime()).slice(0, 5)

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">Son İşlemler</CardTitle>
      </CardHeader>
      <CardContent>
        {sonIslemler.length > 0 ? (
          <div className="space-y-3">
            {sonIslemler.map((islem, i) => (
              <div key={i} className="flex justify-between items-start border-b pb-2 last:border-0 last:pb-0">
                <div>
                  <div className="font-medium">{islem.urunAdi}</div>
                  <div className="text-sm text-muted-foreground">
                    {islem.tarih.toLocaleDateString()} - {islem.musteriAdi || "Müşteri belirtilmedi"}
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium">₺{islem.satisFiyati.toLocaleString()}</div>
                  <div className="text-sm text-green-600">+₺{islem.kar.toLocaleString()}</div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-4 text-muted-foreground">Henüz işlem kaydı bulunmuyor</div>
        )}
      </CardContent>
    </Card>
  )
}

export function EnCokSatanlar() {
  const { islemler } = useIslemler()

  // Ürün bazlı satış istatistikleri
  const urunIstatistikleri = islemler.reduce(
    (acc, islem) => {
      const key = `${islem.kategori}-${islem.urunAdi}`
      if (!acc[key]) {
        acc[key] = {
          kategori: islem.kategori,
          urunAdi: islem.urunAdi,
          adet: 0,
          toplamSatis: 0,
          toplamKar: 0,
        }
      }

      acc[key].adet += 1
      acc[key].toplamSatis += islem.satisFiyati
      acc[key].toplamKar += islem.kar

      return acc
    },
    {} as Record<string, { kategori: string; urunAdi: string; adet: number; toplamSatis: number; toplamKar: number }>,
  )

  // En çok satan 5 ürün
  const enCokSatanlar = Object.values(urunIstatistikleri)
    .sort((a, b) => b.adet - a.adet)
    .slice(0, 5)

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">En Çok Satanlar</CardTitle>
      </CardHeader>
      <CardContent>
        {enCokSatanlar.length > 0 ? (
          <div className="space-y-3">
            {enCokSatanlar.map((urun, i) => (
              <div key={i} className="flex justify-between items-start border-b pb-2 last:border-0 last:pb-0">
                <div>
                  <div className="font-medium">{urun.urunAdi}</div>
                  <div className="text-sm text-muted-foreground">
                    {urun.kategori} - {urun.adet} adet satış
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium">₺{urun.toplamSatis.toLocaleString()}</div>
                  <div className="text-sm text-green-600">+₺{urun.toplamKar.toLocaleString()}</div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-4 text-muted-foreground">Henüz satış verisi bulunmuyor</div>
        )}
      </CardContent>
    </Card>
  )
}
