"use client"

// SQLite veritabanı entegrasyonu
import { useState, useEffect } from "react"

// Veri tipleri (mevcut db.ts ile uyumlu)
export interface Urun {
  id: number
  ad: string
  marka?: string
  model?: string
  kategori: string
  alisFiyati: number
  satisFiyati: number
  stok: number
  ozellikler?: string
  resimUrl: string
}

export interface Islem {
  id: number
  tarih: Date
  kategori: string
  urunId: number
  urunAdi: string
  alisFiyati: number
  satisFiyati: number
  kar: number
  musteriAdi: string
}

export interface Musteri {
  id: number
  ad: string
  telefon?: string
  email?: string
  adres?: string
  notlar?: string
}

// Electron API'sinin mevcut olup olmadığını kontrol et
const isElectron = () => {
  const hasWindow = typeof window !== 'undefined';
  const hasElectronAPI = hasWindow && window.electronAPI;
  const hasDB = hasElectronAPI && window.electronAPI.db;

  console.log('Electron API Check:', {
    hasWindow,
    hasElectronAPI,
    hasDB,
    electronAPI: hasWindow ? !!window.electronAPI : false
  });

  return hasDB;
};

// Veritabanı field mapping (SQLite snake_case -> TypeScript camelCase)
const mapUrunFromDB = (dbUrun: any): Urun => ({
  id: dbUrun.id,
  ad: dbUrun.ad,
  marka: dbUrun.marka,
  model: dbUrun.model,
  kategori: dbUrun.kategori,
  alisFiyati: dbUrun.alis_fiyati,
  satisFiyati: dbUrun.satis_fiyati,
  stok: dbUrun.stok,
  ozellikler: dbUrun.ozellikler,
  resimUrl: dbUrun.resim_url
});

const mapUrunToDB = (urun: Omit<Urun, 'id'>): any => ({
  ad: urun.ad,
  marka: urun.marka,
  model: urun.model,
  kategori: urun.kategori,
  alis_fiyati: urun.alisFiyati,
  satis_fiyati: urun.satisFiyati,
  stok: urun.stok,
  ozellikler: urun.ozellikler,
  resim_url: urun.resimUrl
});

const mapIslemFromDB = (dbIslem: any): Islem => ({
  id: dbIslem.id,
  tarih: new Date(dbIslem.tarih),
  kategori: dbIslem.kategori,
  urunId: dbIslem.urun_id,
  urunAdi: dbIslem.urun_adi,
  alisFiyati: dbIslem.alis_fiyati,
  satisFiyati: dbIslem.satis_fiyati,
  kar: dbIslem.kar,
  musteriAdi: dbIslem.musteri_adi
});

const mapIslemToDB = (islem: Omit<Islem, 'id'>): any => ({
  tarih: islem.tarih.toISOString(),
  kategori: islem.kategori,
  urun_id: islem.urunId,
  urun_adi: islem.urunAdi,
  alis_fiyati: islem.alisFiyati,
  satis_fiyati: islem.satisFiyati,
  kar: islem.kar,
  musteri_adi: islem.musteriAdi
});

const mapMusteriFromDB = (dbMusteri: any): Musteri => ({
  id: dbMusteri.id,
  ad: dbMusteri.ad,
  telefon: dbMusteri.telefon,
  email: dbMusteri.email,
  adres: dbMusteri.adres,
  notlar: dbMusteri.notlar
});

const mapMusteriToDB = (musteri: Omit<Musteri, 'id'>): any => ({
  ad: musteri.ad,
  telefon: musteri.telefon,
  email: musteri.email,
  adres: musteri.adres,
  notlar: musteri.notlar
});

// Ürün işlemleri
export function useUrunler() {
  const [urunler, setUrunler] = useState<Urun[]>([])
  const [yukleniyor, setYukleniyor] = useState(true)

  // Ürünleri yükle
  useEffect(() => {
    loadUrunler()
  }, [])

  const loadUrunler = async () => {
    try {
      if (isElectron()) {
        const dbUrunler = await window.electronAPI.db.getAllUrunler();
        const mappedUrunler = dbUrunler.map(mapUrunFromDB);
        setUrunler(mappedUrunler);
      } else {
        // Fallback: localStorage (web modunda)
        const storedUrunler = localStorage.getItem('telefoncu_urunler');
        if (storedUrunler) {
          setUrunler(JSON.parse(storedUrunler));
        }
      }
    } catch (error) {
      console.error('Error loading products:', error);
    } finally {
      setYukleniyor(false);
    }
  };

  // Ürün ekle
  const urunEkle = async (urun: Omit<Urun, "id">) => {
    try {
      if (isElectron()) {
        const dbUrun = mapUrunToDB(urun);
        const result = await window.electronAPI.db.insertUrun(dbUrun);
        const yeniUrun = mapUrunFromDB(result);
        setUrunler(prev => [...prev, yeniUrun]);
        return yeniUrun;
      } else {
        // Fallback: localStorage
        const yeniUrun = {
          ...urun,
          id: urunler.length > 0 ? Math.max(...urunler.map((u) => u.id)) + 1 : 1,
        };
        const yeniUrunler = [...urunler, yeniUrun];
        setUrunler(yeniUrunler);
        localStorage.setItem('telefoncu_urunler', JSON.stringify(yeniUrunler));
        return yeniUrun;
      }
    } catch (error) {
      console.error('Error adding product:', error);
      throw error;
    }
  };

  // Ürün güncelle
  const urunGuncelle = async (id: number, guncelUrun: Partial<Urun>) => {
    try {
      if (isElectron()) {
        const mevcutUrun = urunler.find(u => u.id === id);
        if (mevcutUrun) {
          const guncellenmisUrun = { ...mevcutUrun, ...guncelUrun };
          const dbUrun = mapUrunToDB(guncellenmisUrun);
          await window.electronAPI.db.updateUrun(id, dbUrun);
          setUrunler(prev => prev.map(urun => urun.id === id ? guncellenmisUrun : urun));
        }
      } else {
        // Fallback: localStorage
        const yeniUrunler = urunler.map((urun) => (urun.id === id ? { ...urun, ...guncelUrun } : urun));
        setUrunler(yeniUrunler);
        localStorage.setItem('telefoncu_urunler', JSON.stringify(yeniUrunler));
      }
    } catch (error) {
      console.error('Error updating product:', error);
      throw error;
    }
  };

  // Ürün sil
  const urunSil = async (id: number) => {
    try {
      if (isElectron()) {
        await window.electronAPI.db.deleteUrun(id);
        setUrunler(prev => prev.filter(urun => urun.id !== id));
      } else {
        // Fallback: localStorage
        const yeniUrunler = urunler.filter((urun) => urun.id !== id);
        setUrunler(yeniUrunler);
        localStorage.setItem('telefoncu_urunler', JSON.stringify(yeniUrunler));
      }
    } catch (error) {
      console.error('Error deleting product:', error);
      throw error;
    }
  };

  // Stok güncelle
  const stokGuncelle = async (id: number, miktar: number) => {
    try {
      if (isElectron()) {
        const success = await window.electronAPI.db.updateStok(id, miktar);
        if (success) {
          setUrunler(prev => prev.map(urun =>
            urun.id === id ? { ...urun, stok: urun.stok + miktar } : urun
          ));
          return true;
        }
        return false;
      } else {
        // Fallback: localStorage
        const urun = urunler.find((u) => u.id === id);
        if (urun) {
          const yeniStok = urun.stok + miktar;
          if (yeniStok >= 0) {
            await urunGuncelle(id, { stok: yeniStok });
            return true;
          }
        }
        return false;
      }
    } catch (error) {
      console.error('Error updating stock:', error);
      return false;
    }
  };

  return { urunler, yukleniyor, urunEkle, urunGuncelle, urunSil, stokGuncelle, loadUrunler };
}

// İşlem işlemleri
export function useIslemler() {
  const [islemler, setIslemler] = useState<Islem[]>([])
  const [yukleniyor, setYukleniyor] = useState(true)

  // İşlemleri yükle
  useEffect(() => {
    loadIslemler()
  }, [])

  const loadIslemler = async () => {
    try {
      if (isElectron()) {
        const dbIslemler = await window.electronAPI.db.getAllIslemler();
        const mappedIslemler = dbIslemler.map(mapIslemFromDB);
        setIslemler(mappedIslemler);
      } else {
        // Fallback: localStorage
        const storedIslemler = localStorage.getItem('telefoncu_islemler');
        if (storedIslemler) {
          const parsedIslemler = JSON.parse(storedIslemler).map((islem: any) => ({
            ...islem,
            tarih: new Date(islem.tarih),
          }));
          setIslemler(parsedIslemler);
        }
      }
    } catch (error) {
      console.error('Error loading transactions:', error);
    } finally {
      setYukleniyor(false);
    }
  };

  // İşlem ekle
  const islemEkle = async (islem: Omit<Islem, "id">) => {
    try {
      if (isElectron()) {
        const dbIslem = mapIslemToDB(islem);
        const result = await window.electronAPI.db.insertIslem(dbIslem);
        const yeniIslem = mapIslemFromDB(result);
        setIslemler(prev => [yeniIslem, ...prev]);
        return yeniIslem;
      } else {
        // Fallback: localStorage
        const yeniIslem = {
          ...islem,
          id: islemler.length > 0 ? Math.max(...islemler.map((i) => i.id)) + 1 : 1,
        };
        const yeniIslemler = [yeniIslem, ...islemler];
        setIslemler(yeniIslemler);
        localStorage.setItem('telefoncu_islemler', JSON.stringify(yeniIslemler));
        return yeniIslem;
      }
    } catch (error) {
      console.error('Error adding transaction:', error);
      throw error;
    }
  };

  // İşlem sil
  const islemSil = async (id: number) => {
    try {
      if (isElectron()) {
        await window.electronAPI.db.deleteIslem(id);
        setIslemler(prev => prev.filter(islem => islem.id !== id));
      } else {
        // Fallback: localStorage
        const yeniIslemler = islemler.filter((islem) => islem.id !== id);
        setIslemler(yeniIslemler);
        localStorage.setItem('telefoncu_islemler', JSON.stringify(yeniIslemler));
      }
      return true;
    } catch (error) {
      console.error('Error deleting transaction:', error);
      return false;
    }
  };

  // İşlem güncelle
  const islemGuncelle = async (id: number, guncelIslem: Partial<Islem>) => {
    try {
      if (isElectron()) {
        const mevcutIslem = islemler.find(i => i.id === id);
        if (mevcutIslem) {
          const guncellenmisIslem = { ...mevcutIslem, ...guncelIslem };
          const dbIslem = mapIslemToDB(guncellenmisIslem);
          await window.electronAPI.db.updateIslem(id, dbIslem);
          setIslemler(prev => prev.map(islem => islem.id === id ? guncellenmisIslem : islem));
        }
      } else {
        // Fallback: localStorage
        const yeniIslemler = islemler.map((islem) =>
          islem.id === id ? { ...islem, ...guncelIslem } : islem
        );
        setIslemler(yeniIslemler);
        localStorage.setItem('telefoncu_islemler', JSON.stringify(yeniIslemler));
      }
      return true;
    } catch (error) {
      console.error('Error updating transaction:', error);
      return false;
    }
  };

  return { islemler, yukleniyor, islemEkle, islemSil, islemGuncelle, loadIslemler };
}

// Müşteri işlemleri
export function useMusteriler() {
  const [musteriler, setMusteriler] = useState<Musteri[]>([])
  const [yukleniyor, setYukleniyor] = useState(true)

  // Müşterileri yükle
  useEffect(() => {
    loadMusteriler()
  }, [])

  const loadMusteriler = async () => {
    try {
      if (isElectron()) {
        const dbMusteriler = await window.electronAPI.db.getAllMusteriler();
        const mappedMusteriler = dbMusteriler.map(mapMusteriFromDB);
        setMusteriler(mappedMusteriler);
      } else {
        // Fallback: localStorage
        const storedMusteriler = localStorage.getItem('telefoncu_musteriler');
        if (storedMusteriler) {
          setMusteriler(JSON.parse(storedMusteriler));
        }
      }
    } catch (error) {
      console.error('Error loading customers:', error);
    } finally {
      setYukleniyor(false);
    }
  };

  // Müşteri ekle
  const musteriEkle = async (musteri: Omit<Musteri, "id">) => {
    try {
      if (isElectron()) {
        const dbMusteri = mapMusteriToDB(musteri);
        const result = await window.electronAPI.db.insertMusteri(dbMusteri);
        const yeniMusteri = mapMusteriFromDB(result);
        setMusteriler(prev => [...prev, yeniMusteri]);
        return yeniMusteri;
      } else {
        // Fallback: localStorage
        const yeniMusteri = {
          ...musteri,
          id: musteriler.length > 0 ? Math.max(...musteriler.map((m) => m.id)) + 1 : 1,
        };
        const yeniMusteriler = [...musteriler, yeniMusteri];
        setMusteriler(yeniMusteriler);
        localStorage.setItem('telefoncu_musteriler', JSON.stringify(yeniMusteriler));
        return yeniMusteri;
      }
    } catch (error) {
      console.error('Error adding customer:', error);
      throw error;
    }
  };

  return { musteriler, yukleniyor, musteriEkle, loadMusteriler };
}

// Rapor oluşturma
export function raporOlustur(islemler: Islem[], baslangicTarihi?: Date, bitisTarihi?: Date) {
  // Tarih filtreleme
  let filtrelenmisIslemler = [...islemler]
  if (baslangicTarihi) {
    filtrelenmisIslemler = filtrelenmisIslemler.filter((islem) => islem.tarih >= baslangicTarihi)
  }
  if (bitisTarihi) {
    filtrelenmisIslemler = filtrelenmisIslemler.filter((islem) => islem.tarih <= bitisTarihi)
  }

  // Toplam değerler
  const toplamIslem = filtrelenmisIslemler.length
  const toplamMaliyet = filtrelenmisIslemler.reduce((sum, islem) => sum + islem.alisFiyati, 0)
  const toplamSatis = filtrelenmisIslemler.reduce((sum, islem) => sum + islem.satisFiyati, 0)
  const toplamKar = filtrelenmisIslemler.reduce((sum, islem) => sum + islem.kar, 0)
  const karMarji = toplamSatis > 0 ? (toplamKar / toplamSatis) * 100 : 0

  // Kategori bazlı özet
  const kategoriBazli = filtrelenmisIslemler.reduce(
    (acc, islem) => {
      if (!acc[islem.kategori]) {
        acc[islem.kategori] = {
          islemSayisi: 0,
          toplamMaliyet: 0,
          toplamSatis: 0,
          toplamKar: 0,
        }
      }

      acc[islem.kategori].islemSayisi += 1
      acc[islem.kategori].toplamMaliyet += islem.alisFiyati
      acc[islem.kategori].toplamSatis += islem.satisFiyati
      acc[islem.kategori].toplamKar += islem.kar

      return acc
    },
    {} as Record<string, { islemSayisi: number; toplamMaliyet: number; toplamSatis: number; toplamKar: number }>,
  )

  // Günlük özet
  const gunlukOzet = filtrelenmisIslemler.reduce(
    (acc, islem) => {
      const tarihStr = islem.tarih.toISOString().split("T")[0]

      if (!acc[tarihStr]) {
        acc[tarihStr] = {
          tarih: islem.tarih,
          islemSayisi: 0,
          toplamMaliyet: 0,
          toplamSatis: 0,
          toplamKar: 0,
        }
      }

      acc[tarihStr].islemSayisi += 1
      acc[tarihStr].toplamMaliyet += islem.alisFiyati
      acc[tarihStr].toplamSatis += islem.satisFiyati
      acc[tarihStr].toplamKar += islem.kar

      return acc
    },
    {} as Record<
      string,
      { tarih: Date; islemSayisi: number; toplamMaliyet: number; toplamSatis: number; toplamKar: number }
    >,
  )

  return {
    toplamIslem,
    toplamMaliyet,
    toplamSatis,
    toplamKar,
    karMarji,
    kategoriBazli,
    gunlukOzet,
    islemler: filtrelenmisIslemler,
  }
}

// CSV formatında rapor indirme
export function raporuIndir(islemler: Islem[], baslangicTarihi?: Date, bitisTarihi?: Date) {
  const rapor = raporOlustur(islemler, baslangicTarihi, bitisTarihi)

  // CSV başlıkları
  let csv = "ID,Tarih,Kategori,Ürün Adı,Alış Fiyatı,Satış Fiyatı,Kar,Müşteri Adı\n"

  // İşlemleri CSV'ye ekle
  rapor.islemler.forEach((islem) => {
    csv += `${islem.id},${islem.tarih.toLocaleDateString()},${islem.kategori},${islem.urunAdi},${islem.alisFiyati},${islem.satisFiyati},${islem.kar},${islem.musteriAdi}\n`
  })

  // Özet bilgileri ekle
  csv += "\nÖzet Bilgiler\n"
  csv += `Toplam İşlem,${rapor.toplamIslem}\n`
  csv += `Toplam Maliyet,${rapor.toplamMaliyet}\n`
  csv += `Toplam Satış,${rapor.toplamSatis}\n`
  csv += `Toplam Kar,${rapor.toplamKar}\n`
  csv += `Kar Marjı,%${rapor.karMarji.toFixed(2)}\n`

  // Kategori bazlı özet
  csv += "\nKategori Bazlı Özet\n"
  csv += "Kategori,İşlem Sayısı,Toplam Maliyet,Toplam Satış,Toplam Kar,Kar Marjı\n"
  Object.entries(rapor.kategoriBazli).forEach(([kategori, data]) => {
    const karMarji = data.toplamSatis > 0 ? (data.toplamKar / data.toplamSatis) * 100 : 0
    csv += `${kategori},${data.islemSayisi},${data.toplamMaliyet},${data.toplamSatis},${data.toplamKar},%${karMarji.toFixed(2)}\n`
  })

  // CSV dosyasını indir
  const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" })
  const url = URL.createObjectURL(blob)
  const link = document.createElement("a")
  link.setAttribute("href", url)
  link.setAttribute("download", `telefoncu_rapor_${new Date().toISOString().split("T")[0]}.csv`)
  link.style.visibility = "hidden"
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
