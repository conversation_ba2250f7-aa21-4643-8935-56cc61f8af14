// Renderer süreç yardımcı fonksiyonları
// Bu dosya isteğe bağlıdır ve gelecekte ek özellikler için kullanılabilir

// Electron API'sinin mevcut olup olmadığını kontrol et
const isElectron = () => {
  return typeof window !== 'undefined' && window.electronAPI;
};

// Platform kontrolü
const getPlatform = () => {
  if (isElectron()) {
    return window.electronAPI.platform;
  }
  return 'web';
};

// Uygulama versiyonunu al
const getAppVersion = () => {
  if (isElectron()) {
    return window.electronAPI.getAppVersion();
  }
  return '0.1.0';
};

// Bildirim göster
const showNotification = (title, body) => {
  if (isElectron()) {
    return window.electronAPI.showNotification(title, body);
  } else {
    // Web için fallback
    if ('Notification' in window) {
      new Notification(title, { body });
    }
  }
};

// <PERSON><PERSON> olaylarını dinle
const setupMenuListeners = () => {
  if (isElectron()) {
    window.electronAPI.onMenuAction((event, action) => {
      switch (action) {
        case 'new-transaction':
          // Yeni işlem sayfasına yönlendir
          if (typeof window !== 'undefined' && window.location) {
            window.location.href = '/';
          }
          break;
        case 'generate-report':
          // Rapor sayfasına yönlendir
          if (typeof window !== 'undefined' && window.location) {
            window.location.href = '/raporlar';
          }
          break;
      }
    });
  }
};

// Temizlik fonksiyonu
const cleanup = () => {
  if (isElectron()) {
    window.electronAPI.removeMenuListeners();
  }
};

// Export fonksiyonlar (eğer modül sistemi kullanılıyorsa)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    isElectron,
    getPlatform,
    getAppVersion,
    showNotification,
    setupMenuListeners,
    cleanup
  };
}

// Global olarak kullanılabilir hale getir
if (typeof window !== 'undefined') {
  window.electronUtils = {
    isElectron,
    getPlatform,
    getAppVersion,
    showNotification,
    setupMenuListeners,
    cleanup
  };
}
