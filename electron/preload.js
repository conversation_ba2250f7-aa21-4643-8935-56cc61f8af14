const { contextBridge, ipcRenderer } = require('electron');

// Güvenli API'leri renderer sürecine maruz bırak
contextBridge.exposeInMainWorld('electronAPI', {
  // <PERSON><PERSON> olaylarını dinle
  onMenuAction: (callback) => {
    ipcRenderer.on('new-transaction', callback);
    ipcRenderer.on('generate-report', callback);
  },

  // Menü olay dinleyicilerini kaldır
  removeMenuListeners: () => {
    ipcRenderer.removeAllListeners('new-transaction');
    ipcRenderer.removeAllListeners('generate-report');
  },

  // Platform bilgisi
  platform: process.platform,

  // Uygulama bilgileri
  getAppVersion: () => {
    return process.env.npm_package_version || '0.1.0';
  },

  // Dosya sistemi işlemleri (gelecekte kullanım için)
  saveFile: (data, filename) => {
    return ipcRenderer.invoke('save-file', data, filename);
  },

  loadFile: (filename) => {
    return ipcRenderer.invoke('load-file', filename);
  },

  // Bildirim gönder
  showNotification: (title, body) => {
    return ipcRenderer.invoke('show-notification', title, body);
  },

  // Veritabanı API'leri
  db: {
    // Ürün işlemleri
    getAllUrunler: () => ipcRenderer.invoke('db:getAllUrunler'),
    getUrunById: (id) => ipcRenderer.invoke('db:getUrunById', id),
    insertUrun: (urun) => ipcRenderer.invoke('db:insertUrun', urun),
    updateUrun: (id, urun) => ipcRenderer.invoke('db:updateUrun', id, urun),
    deleteUrun: (id) => ipcRenderer.invoke('db:deleteUrun', id),
    updateStok: (id, miktar) => ipcRenderer.invoke('db:updateStok', id, miktar),

    // İşlem işlemleri
    getAllIslemler: () => ipcRenderer.invoke('db:getAllIslemler'),
    getIslemById: (id) => ipcRenderer.invoke('db:getIslemById', id),
    insertIslem: (islem) => ipcRenderer.invoke('db:insertIslem', islem),
    updateIslem: (id, islem) => ipcRenderer.invoke('db:updateIslem', id, islem),
    deleteIslem: (id) => ipcRenderer.invoke('db:deleteIslem', id),

    // Müşteri işlemleri
    getAllMusteriler: () => ipcRenderer.invoke('db:getAllMusteriler'),
    getMusteriById: (id) => ipcRenderer.invoke('db:getMusteriById', id),
    insertMusteri: (musteri) => ipcRenderer.invoke('db:insertMusteri', musteri),
    updateMusteri: (id, musteri) => ipcRenderer.invoke('db:updateMusteri', id, musteri),
    deleteMusteri: (id) => ipcRenderer.invoke('db:deleteMusteri', id),

    // Rapor işlemleri
    getRaporData: (baslangicTarihi, bitisTarihi) => ipcRenderer.invoke('db:getRaporData', baslangicTarihi, bitisTarihi)
  }
});

// Konsol loglarını ana sürece gönder (geliştirme için)
console.log('Preload script starting...');

window.addEventListener('DOMContentLoaded', () => {
  console.log('Electron preload script loaded - DOM ready');
});

// Hata ayıklama için global error handler
window.addEventListener('error', (event) => {
  console.error('Window error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
});

// Electron API'sinin yüklendiğini kontrol et
console.log('ElectronAPI available:', !!window.electronAPI);
