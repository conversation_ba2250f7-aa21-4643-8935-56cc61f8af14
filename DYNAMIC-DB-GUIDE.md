# 🔄 Dinamik SQLite Veritabanı Bağlantı Kılavuzu

## 🎯 Genel Bakış

Bu kılavuz, **herhangi bir bilgisayarda** SQLite veritabanına nasıl bağlanacağınızı gösterir. Artık sabit yollar kullanmıyoruz - sistem otomatik olarak doğru veritabanı yolunu bulur!

## 🚀 Hızlı Komutlar (Tüm Platformlar)

### NPM Komutları:
```bash
# Veritabanı yolunu öğren
npm run db:path

# Veritabanı durumunu kontrol et
npm run db:info

# Hızlı istatistikler
npm run db:stats

# SQLite shell aç
npm run db:shell

# Yedek oluştur
npm run db:backup

# Platform scriptleri oluştur
npm run db:scripts

# Veritabanı yöneticisi
npm run db:manager
```

## 📍 Platform-Specific Yollar

### Windows:
```
%APPDATA%\telefoncu-takip-electron\telefoncu-takip.db

Örnek:
C:\Users\<USER>\AppData\Roaming\telefoncu-takip-electron\telefoncu-takip.db
```

### macOS:
```
~/Library/Application Support/telefoncu-takip-electron/telefoncu-takip.db

Örnek:
/Users/<USER>/Library/Application Support/telefoncu-takip-electron/telefoncu-takip.db
```

### Linux:
```
~/.config/telefoncu-takip-electron/telefoncu-takip.db

Örnek:
/home/<USER>/.config/telefoncu-takip-electron/telefoncu-takip.db
```

## 🔧 Platform-Specific Bağlantı

### Windows Kullanıcıları:

#### 1. Dinamik Yol Alma:
```cmd
npm run db:path
```

#### 2. Windows Batch Script:
```cmd
npm run db:scripts
scripts\db-manager.bat
```

#### 3. PowerShell:
```powershell
npm run db:scripts
.\scripts\db-manager.ps1
```

#### 4. SQLite Command Line:
```cmd
# Önce yolu öğren
npm run db:path

# Sonra bağlan (örnek)
sqlite3.exe "C:\Users\<USER>\AppData\Roaming\telefoncu-takip-electron\telefoncu-takip.db"
```

### macOS/Linux Kullanıcıları:

#### 1. Dinamik Yol Alma:
```bash
npm run db:path
```

#### 2. Shell Script:
```bash
npm run db:scripts
./scripts/db-manager.sh
```

#### 3. SQLite Command Line:
```bash
# Önce yolu öğren
DB_PATH=$(npm run db:path --silent)

# Sonra bağlan
sqlite3 "$DB_PATH"
```

## 🛠️ Otomatik Script Oluşturma

### Platform Scriptleri Oluştur:
```bash
npm run db:scripts
```

Bu komut platformunuza göre otomatik olarak oluşturur:
- **Windows**: `db-manager.bat` ve `db-manager.ps1`
- **macOS/Linux**: `db-manager.sh`

### Scriptleri Çalıştır:

#### Windows:
```cmd
# Batch script
scripts\db-manager.bat

# PowerShell script
.\scripts\db-manager.ps1
```

#### macOS/Linux:
```bash
# Shell script
./scripts/db-manager.sh
```

## 📊 Örnek Kullanım Senaryoları

### 1. Yeni Bilgisayarda İlk Kurulum:
```bash
# 1. Projeyi kur
npm install --legacy-peer-deps

# 2. Uygulamayı çalıştır (veritabanı oluşturulur)
npm run electron

# 3. Veritabanı yolunu öğren
npm run db:path

# 4. Bağlantıyı test et
npm run db:stats
```

### 2. Farklı Kullanıcı Hesabında:
```bash
# Veritabanı yolu otomatik olarak kullanıcıya göre ayarlanır
npm run db:info
```

### 3. Veritabanı Yedekleme:
```bash
# Otomatik yedek
npm run db:backup

# Manuel yedek
DB_PATH=$(npm run db:path --silent)
cp "$DB_PATH" "backup_$(date +%Y%m%d).db"
```

### 4. Veritabanı Taşıma:
```bash
# Kaynak bilgisayarda
npm run db:backup

# Hedef bilgisayarda
npm run electron  # Veritabanı klasörü oluşturulur
# Yedek dosyasını kopyala
```

## 🔍 Sorun Giderme

### Veritabanı Bulunamıyor:
```bash
# 1. Önce uygulamayı çalıştır
npm run electron

# 2. Yolu kontrol et
npm run db:path

# 3. Dosya varlığını kontrol et
npm run db:info
```

### İzin Sorunları:
```bash
# Windows
icacls "%APPDATA%\telefoncu-takip-electron" /grant %USERNAME%:F

# macOS/Linux
chmod 755 ~/Library/Application\ Support/telefoncu-takip-electron/
chmod 644 ~/Library/Application\ Support/telefoncu-takip-electron/telefoncu-takip.db
```

### SQLite Kurulu Değil:
```bash
# Windows
# https://sqlite.org/download.html adresinden indirin

# macOS
brew install sqlite

# Ubuntu/Debian
sudo apt install sqlite3

# CentOS/RHEL
sudo yum install sqlite
```

## 🎯 EXE Dağıtımı için Notlar

### Windows EXE Kullanıcıları:
1. **Kurulum**: EXE dosyasını çalıştırın
2. **İlk Çalıştırma**: Veritabanı otomatik oluşturulur
3. **Veri Konumu**: `%APPDATA%\telefoncu-takip-electron\`
4. **Yedekleme**: Bu klasörü yedekleyin

### Portable EXE Kullanıcıları:
1. **Çalıştırma**: EXE dosyasını herhangi bir klasörde çalıştırın
2. **Veri Konumu**: Kullanıcı profili altında oluşturulur
3. **Taşınabilirlik**: EXE taşınabilir, veriler kullanıcıya bağlı

## 📋 Komut Referansı

| Komut | Açıklama | Platform |
|-------|----------|----------|
| `npm run db:path` | Veritabanı yolunu göster | Tümü |
| `npm run db:info` | Veritabanı durumu | Tümü |
| `npm run db:stats` | Hızlı istatistikler | Tümü |
| `npm run db:shell` | SQLite shell aç | Tümü |
| `npm run db:backup` | Yedek oluştur | Tümü |
| `npm run db:scripts` | Platform scriptleri oluştur | Tümü |
| `npm run db:manager` | Veritabanı yöneticisi | Tümü |
| `npm run db:commands` | Platform komutları göster | Tümü |

## 🔐 Güvenlik Notları

1. **Veri Konumu**: Kullanıcı profili altında güvenli
2. **İzinler**: Sadece kullanıcının erişimi var
3. **Yedekleme**: Düzenli yedek alın
4. **Şifreleme**: Hassas veriler için ek şifreleme düşünün

## 🚀 Gelecek Özellikler

- 🔄 **Otomatik Yedekleme**: Zamanlı yedekleme sistemi
- 🌐 **Bulut Senkronizasyonu**: Google Drive, Dropbox entegrasyonu
- 🔐 **Veritabanı Şifreleme**: SQLCipher entegrasyonu
- 📱 **Mobil Senkronizasyon**: Telefon uygulaması ile senkronizasyon

Bu kılavuz ile artık herhangi bir bilgisayarda kolayca veritabanınıza erişebilirsiniz! 🎉
