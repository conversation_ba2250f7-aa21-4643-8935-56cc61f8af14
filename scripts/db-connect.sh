#!/bin/bash
# SQLite Veritabanı Bağlantı Scripti - Unix/Linux/macOS

echo "SQLite Veritabanı Bağlantı Scripti - Unix/Linux/macOS"
echo "=================================================="
echo ""
echo "Veritabanı Yolu: /Users/<USER>/Library/Application Support/telefoncu-takip-electron/telefoncu-takip.db"
echo ""

if [ ! -f "/Users/<USER>/Library/Application Support/telefoncu-takip-electron/telefoncu-takip.db" ]; then
    echo "HATA: Veritabanı dosyası bulunamadı!"
    echo "Önce Electron uygulamasını çalıştırın: npm run electron"
    read -p "Devam etmek için Enter'a basın..."
    exit 1
fi

echo "Mevcut tablolar:"
sqlite3 "/Users/<USER>/Library/Application Support/telefoncu-takip-electron/telefoncu-takip.db" ".tables"
echo ""

echo "Ürün sayısı:"
sqlite3 "/Users/<USER>/Library/Application Support/telefoncu-takip-electron/telefoncu-takip.db" "SELECT COUNT(*) as urun_sayisi FROM urunler;"
echo ""

echo "İşlem sayısı:"
sqlite3 "/Users/<USER>/Library/Application Support/telefoncu-takip-electron/telefoncu-takip.db" "SELECT COUNT(*) as islem_sayisi FROM islemler;"
echo ""

echo "SQLite shell başlatılıyor..."
echo "Çıkmak için .quit yazın"
sqlite3 "/Users/<USER>/Library/Application Support/telefoncu-takip-electron/telefoncu-takip.db"
