"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Phone, Mail, MapPin, Search, UserPlus, History } from "lucide-react"

interface Musteri {
  id: number
  ad: string
  soyad: string
  telefon: string
  email: string
  adres: string
  notlar: string
  kayitTarihi: Date
  sonIslemTarihi: Date | null
  toplamHarcama: number
  islemSayisi: number
}

interface IslemGecmisi {
  id: number
  musteriId: number
  tarih: Date
  islemTuru: string
  urun: string
  tutar: number
  durum: string
  notlar: string
}

export default function MusteriTakip() {
  const [musteriler, setMusteriler] = useState<Musteri[]>([
    {
      id: 1,
      ad: "Ah<PERSON>",
      soyad: "<PERSON><PERSON><PERSON><PERSON>",
      telefon: "0532 123 4567",
      email: "<EMAIL>",
      adres: "Kadıköy, İstanbul",
      notlar: "VIP müşteri",
      kayitTarihi: new Date("2023-01-15"),
      sonIslemTarihi: new Date("2025-05-10"),
      toplamHarcama: 4500,
      islemSayisi: 3,
    },
    {
      id: 2,
      ad: "Ayşe",
      soyad: "Kaya",
      telefon: "0533 456 7890",
      email: "<EMAIL>",
      adres: "Beşiktaş, İstanbul",
      notlar: "",
      kayitTarihi: new Date("2023-03-22"),
      sonIslemTarihi: new Date("2025-04-28"),
      toplamHarcama: 2200,
      islemSayisi: 2,
    },
    {
      id: 3,
      ad: "Mehmet",
      soyad: "Demir",
      telefon: "0535 789 0123",
      email: "<EMAIL>",
      adres: "Beyoğlu, İstanbul",
      notlar: "Ekran kırılmalarına karşı garanti istedi",
      kayitTarihi: new Date("2023-06-10"),
      sonIslemTarihi: null,
      toplamHarcama: 0,
      islemSayisi: 0,
    },
  ])

  const [islemGecmisi, setIslemGecmisi] = useState<IslemGecmisi[]>([
    {
      id: 1,
      musteriId: 1,
      tarih: new Date("2025-05-10"),
      islemTuru: "Telefon Satış",
      urun: "iPhone 13",
      tutar: 22000,
      durum: "Tamamlandı",
      notlar: "",
    },
    {
      id: 2,
      musteriId: 1,
      tarih: new Date("2025-03-15"),
      islemTuru: "Aksesuar Satış",
      urun: "Silikon Kılıf",
      tutar: 150,
      durum: "Tamamlandı",
      notlar: "",
    },
    {
      id: 3,
      musteriId: 1,
      tarih: new Date("2025-01-20"),
      islemTuru: "Tamir",
      urun: "Ekran Değişimi",
      tutar: 1500,
      durum: "Tamamlandı",
      notlar: "Garanti kapsamında",
    },
    {
      id: 4,
      musteriId: 2,
      tarih: new Date("2025-04-28"),
      islemTuru: "Telefon Satış",
      urun: "Samsung Galaxy S22",
      tutar: 18500,
      durum: "Tamamlandı",
      notlar: "",
    },
    {
      id: 5,
      musteriId: 2,
      tarih: new Date("2025-02-10"),
      islemTuru: "Aksesuar Satış",
      urun: "Temperli Cam",
      tutar: 100,
      durum: "Tamamlandı",
      notlar: "",
    },
  ])

  const [searchTerm, setSearchTerm] = useState("")
  const [selectedMusteri, setSelectedMusteri] = useState<Musteri | null>(null)

  const filteredMusteriler = musteriler.filter(
    (musteri) =>
      musteri.ad.toLowerCase().includes(searchTerm.toLowerCase()) ||
      musteri.soyad.toLowerCase().includes(searchTerm.toLowerCase()) ||
      musteri.telefon.includes(searchTerm),
  )

  const musteriIslemleri = islemGecmisi.filter((islem) => islem.musteriId === selectedMusteri?.id)

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold text-center mb-6">Müşteri Takip Sistemi</h1>

      <Tabs defaultValue="musteriler">
        <TabsList className="mb-4">
          <TabsTrigger value="musteriler">Müşteri Listesi</TabsTrigger>
          <TabsTrigger value="detay" disabled={!selectedMusteri}>
            Müşteri Detayı
          </TabsTrigger>
        </TabsList>

        <TabsContent value="musteriler">
          <div className="flex gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Müşteri ara..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button>
              <UserPlus className="mr-2 h-4 w-4" /> Yeni Müşteri Ekle
            </Button>
          </div>

          <Card>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Müşteri</TableHead>
                      <TableHead>Telefon</TableHead>
                      <TableHead>E-posta</TableHead>
                      <TableHead>Kayıt Tarihi</TableHead>
                      <TableHead>Son İşlem</TableHead>
                      <TableHead>Toplam Harcama</TableHead>
                      <TableHead>İşlem Sayısı</TableHead>
                      <TableHead>İşlemler</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredMusteriler.map((musteri) => (
                      <TableRow key={musteri.id}>
                        <TableCell>
                          {musteri.ad} {musteri.soyad}
                        </TableCell>
                        <TableCell>{musteri.telefon}</TableCell>
                        <TableCell>{musteri.email}</TableCell>
                        <TableCell>{musteri.kayitTarihi.toLocaleDateString("tr-TR")}</TableCell>
                        <TableCell>
                          {musteri.sonIslemTarihi ? musteri.sonIslemTarihi.toLocaleDateString("tr-TR") : "-"}
                        </TableCell>
                        <TableCell>₺{musteri.toplamHarcama.toLocaleString()}</TableCell>
                        <TableCell>{musteri.islemSayisi}</TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setSelectedMusteri(musteri)
                                document.querySelector('[data-value="detay"]')?.click()
                              }}
                            >
                              Detay
                            </Button>
                            <Button variant="outline" size="sm">
                              Düzenle
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="detay">
          {selectedMusteri && (
            <div className="space-y-6">
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedMusteri(null)
                  document.querySelector('[data-value="musteriler"]')?.click()
                }}
              >
                ← Listeye Dön
              </Button>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardContent className="p-6">
                    <h2 className="text-xl font-semibold mb-4">
                      {selectedMusteri.ad} {selectedMusteri.soyad}
                    </h2>
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span>{selectedMusteri.telefon}</span>
                      </div>
                      <div className="flex items-center">
                        <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span>{selectedMusteri.email}</span>
                      </div>
                      <div className="flex items-start">
                        <MapPin className="h-4 w-4 mr-2 text-muted-foreground mt-1" />
                        <span>{selectedMusteri.adres}</span>
                      </div>
                      <div className="pt-2">
                        <p className="text-sm text-muted-foreground mb-1">Notlar:</p>
                        <p>{selectedMusteri.notlar || "Not bulunmuyor."}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <h2 className="text-xl font-semibold mb-4">Müşteri Özeti</h2>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-muted-foreground">Kayıt Tarihi</p>
                        <p className="font-medium">{selectedMusteri.kayitTarihi.toLocaleDateString("tr-TR")}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Son İşlem</p>
                        <p className="font-medium">
                          {selectedMusteri.sonIslemTarihi
                            ? selectedMusteri.sonIslemTarihi.toLocaleDateString("tr-TR")
                            : "-"}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Toplam Harcama</p>
                        <p className="font-medium">₺{selectedMusteri.toplamHarcama.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">İşlem Sayısı</p>
                        <p className="font-medium">{selectedMusteri.islemSayisi}</p>
                      </div>
                    </div>
                    <div className="mt-4 pt-4 border-t">
                      <Button className="w-full">
                        <History className="mr-2 h-4 w-4" /> Yeni İşlem Ekle
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardContent className="p-6">
                  <h2 className="text-xl font-semibold mb-4">İşlem Geçmişi</h2>
                  {musteriIslemleri.length > 0 ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Tarih</TableHead>
                          <TableHead>İşlem Türü</TableHead>
                          <TableHead>Ürün/Hizmet</TableHead>
                          <TableHead>Tutar</TableHead>
                          <TableHead>Durum</TableHead>
                          <TableHead>Notlar</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {musteriIslemleri.map((islem) => (
                          <TableRow key={islem.id}>
                            <TableCell>{islem.tarih.toLocaleDateString("tr-TR")}</TableCell>
                            <TableCell>{islem.islemTuru}</TableCell>
                            <TableCell>{islem.urun}</TableCell>
                            <TableCell>₺{islem.tutar.toLocaleString()}</TableCell>
                            <TableCell>{islem.durum}</TableCell>
                            <TableCell>{islem.notlar || "-"}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <p className="text-center py-4 text-muted-foreground">Bu müşteriye ait işlem bulunamadı.</p>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
