"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ArrowLeft, Edit, Plus, Save, Search, Trash, ShoppingCart } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { useUrunler, type Urun } from "@/lib/db"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

export default function TelefonlarPage() {
  const router = useRouter()
  const { urun<PERSON>, urun<PERSON><PERSON><PERSON><PERSON>, urun<PERSON>il, urun<PERSON>kle } = useUrunler()

  const [searchTerm, setSearchTerm] = useState("")
  const [editingId, setEditingId] = useState<number | null>(null)
  const [editForm, setEditForm] = useState<Urun | null>(null)
  const [yeniTelefon, setYeniTelefon] = useState<Omit<Urun, "id">>({
    ad: "",
    marka: "",
    model: "",
    kategori: "Telefon",
    alisFiyati: 0,
    satisFiyati: 0,
    stok: 0,
    ozellikler: "",
    resimUrl: "/placeholder.svg?height=80&width=80",
  })

  // Sadece telefonları filtrele
  const telefonlar = urunler.filter((urun) => urun.kategori === "Telefon")

  const filteredTelefonlar = telefonlar.filter(
    (telefon) =>
      telefon.ad.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (telefon.marka && telefon.marka.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (telefon.model && telefon.model.toLowerCase().includes(searchTerm.toLowerCase())),
  )

  const handleEdit = (telefon: Urun) => {
    setEditingId(telefon.id)
    setEditForm({ ...telefon })
  }

  const handleSave = () => {
    if (!editForm) return

    urunGuncelle(editForm.id, editForm)
    setEditingId(null)
    setEditForm(null)
    toast({
      title: "Başarılı",
      description: "Telefon bilgileri güncellendi",
    })
  }

  const handleCancel = () => {
    setEditingId(null)
    setEditForm(null)
  }

  const handleDelete = (id: number) => {
    urunSil(id)
    toast({
      title: "Başarılı",
      description: "Telefon silindi",
    })
  }

  const handleYeniTelefonEkle = () => {
    // Validasyon
    if (!yeniTelefon.ad || !yeniTelefon.marka || !yeniTelefon.model) {
      toast({
        title: "Hata",
        description: "Lütfen tüm zorunlu alanları doldurun",
        variant: "destructive",
      })
      return
    }

    // Yeni telefon ekle
    urunEkle(yeniTelefon)

    // Formu temizle
    setYeniTelefon({
      ad: "",
      marka: "",
      model: "",
      kategori: "Telefon",
      alisFiyati: 0,
      satisFiyati: 0,
      stok: 0,
      ozellikler: "",
      resimUrl: "/placeholder.svg?height=80&width=80",
    })

    toast({
      title: "Başarılı",
      description: "Yeni telefon başarıyla eklendi",
    })
  }

  const handleSelectTelefon = (telefon: Urun) => {
    // Telefon bilgilerini URL parametresi olarak gönder
    router.push(`/?urunId=${telefon.id}`)
  }

  return (
    <div className="container mx-auto p-4">
      <Toaster />
      <div className="flex items-center mb-6">
        <Link href="/urunler">
          <Button variant="outline" size="sm" className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" /> Geri
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">Telefonlar</h1>
      </div>

      <div className="flex justify-between mb-6">
        <div className="relative w-full max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Telefon ara..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Dialog>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" /> Yeni Telefon Ekle
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Yeni Telefon Ekle</DialogTitle>
              <DialogDescription>
                Yeni telefon bilgilerini girin. Tüm alanları doldurduğunuzdan emin olun.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="marka" className="text-right">
                  Marka
                </label>
                <Input
                  id="marka"
                  value={yeniTelefon.marka || ""}
                  onChange={(e) => setYeniTelefon({ ...yeniTelefon, marka: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="model" className="text-right">
                  Model
                </label>
                <Input
                  id="model"
                  value={yeniTelefon.model || ""}
                  onChange={(e) => setYeniTelefon({ ...yeniTelefon, model: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="ad" className="text-right">
                  Ürün Adı
                </label>
                <Input
                  id="ad"
                  value={yeniTelefon.ad}
                  onChange={(e) => setYeniTelefon({ ...yeniTelefon, ad: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="ozellikler" className="text-right">
                  Özellikler
                </label>
                <Input
                  id="ozellikler"
                  value={yeniTelefon.ozellikler || ""}
                  onChange={(e) => setYeniTelefon({ ...yeniTelefon, ozellikler: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="alisFiyati" className="text-right">
                  Alış Fiyatı
                </label>
                <Input
                  id="alisFiyati"
                  type="number"
                  value={yeniTelefon.alisFiyati || ""}
                  onChange={(e) => setYeniTelefon({ ...yeniTelefon, alisFiyati: Number(e.target.value) })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="satisFiyati" className="text-right">
                  Satış Fiyatı
                </label>
                <Input
                  id="satisFiyati"
                  type="number"
                  value={yeniTelefon.satisFiyati || ""}
                  onChange={(e) => setYeniTelefon({ ...yeniTelefon, satisFiyati: Number(e.target.value) })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="stok" className="text-right">
                  Stok
                </label>
                <Input
                  id="stok"
                  type="number"
                  value={yeniTelefon.stok || ""}
                  onChange={(e) => setYeniTelefon({ ...yeniTelefon, stok: Number(e.target.value) })}
                  className="col-span-3"
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="submit" onClick={handleYeniTelefonEkle}>
                Ekle
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Görsel</TableHead>
                  <TableHead>Marka</TableHead>
                  <TableHead>Model</TableHead>
                  <TableHead>Özellikler</TableHead>
                  <TableHead>Alış Fiyatı (₺)</TableHead>
                  <TableHead>Satış Fiyatı (₺)</TableHead>
                  <TableHead>Stok</TableHead>
                  <TableHead>İşlemler</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTelefonlar.length > 0 ? (
                  filteredTelefonlar.map((telefon) => (
                    <TableRow key={telefon.id}>
                      <TableCell>
                        <Image
                          src={telefon.resimUrl || "/placeholder.svg"}
                          alt={telefon.ad}
                          width={50}
                          height={50}
                          className="rounded-md object-cover"
                        />
                      </TableCell>
                      <TableCell>
                        {editingId === telefon.id ? (
                          <Input
                            value={editForm?.marka || ""}
                            onChange={(e) => setEditForm({ ...editForm!, marka: e.target.value })}
                          />
                        ) : (
                          telefon.marka
                        )}
                      </TableCell>
                      <TableCell>
                        {editingId === telefon.id ? (
                          <Input
                            value={editForm?.model || ""}
                            onChange={(e) => setEditForm({ ...editForm!, model: e.target.value })}
                          />
                        ) : (
                          telefon.model
                        )}
                      </TableCell>
                      <TableCell>
                        {editingId === telefon.id ? (
                          <Input
                            value={editForm?.ozellikler || ""}
                            onChange={(e) => setEditForm({ ...editForm!, ozellikler: e.target.value })}
                          />
                        ) : (
                          telefon.ozellikler
                        )}
                      </TableCell>
                      <TableCell>
                        {editingId === telefon.id ? (
                          <Input
                            type="number"
                            value={editForm?.alisFiyati}
                            onChange={(e) => setEditForm({ ...editForm!, alisFiyati: Number(e.target.value) })}
                          />
                        ) : (
                          telefon.alisFiyati.toLocaleString()
                        )}
                      </TableCell>
                      <TableCell>
                        {editingId === telefon.id ? (
                          <Input
                            type="number"
                            value={editForm?.satisFiyati}
                            onChange={(e) => setEditForm({ ...editForm!, satisFiyati: Number(e.target.value) })}
                          />
                        ) : (
                          telefon.satisFiyati.toLocaleString()
                        )}
                      </TableCell>
                      <TableCell>
                        {editingId === telefon.id ? (
                          <Input
                            type="number"
                            value={editForm?.stok}
                            onChange={(e) => setEditForm({ ...editForm!, stok: Number(e.target.value) })}
                          />
                        ) : (
                          <div className="flex items-center">
                            {telefon.stok}
                            {telefon.stok <= 2 && (
                              <Badge variant="destructive" className="ml-2">
                                Az
                              </Badge>
                            )}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        {editingId === telefon.id ? (
                          <div className="flex space-x-2">
                            <Button size="sm" onClick={handleSave}>
                              <Save className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline" onClick={handleCancel}>
                              İptal
                            </Button>
                          </div>
                        ) : (
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="default"
                              className="bg-green-600 hover:bg-green-700"
                              onClick={() => handleSelectTelefon(telefon)}
                            >
                              <ShoppingCart className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => handleEdit(telefon)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="text-red-500"
                              onClick={() => handleDelete(telefon.id)}
                            >
                              <Trash className="h-4 w-4" />
                            </Button>
                          </div>
                        )}
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-4 text-muted-foreground">
                      Telefon bulunamadı
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
