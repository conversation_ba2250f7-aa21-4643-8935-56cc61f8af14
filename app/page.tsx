"use client"

import { useState, useEffect } from "react"
import { CalendarIcon, Search, Edit, Trash, Save, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { format } from "date-fns"
import { tr } from "date-fns/locale"
import Link from "next/link"
import { useSearchParams } from "next/navigation"
import { useUrunler, useIslemler, type Islem } from "@/lib/db"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

export default function TelefonTakip() {
  const searchParams = useSearchParams()
  const { urunler, stokGuncelle } = useUrunler()
  const { islemler, islemEkle, islemSil, islemGuncelle } = useIslemler()

  const [newTransaction, setNewTransaction] = useState({
    tarih: new Date(),
    kategori: "Telefon",
    urunId: 0,
    urunAdi: "",
    alisFiyati: 0,
    satisFiyati: 0,
    musteriAdi: "",
  })

  const [filterStartDate, setFilterStartDate] = useState<Date | undefined>(
    new Date(new Date().setDate(new Date().getDate() - 7)),
  )
  const [filterEndDate, setFilterEndDate] = useState<Date | undefined>(new Date())
  const [filterCategory, setFilterCategory] = useState<string>("Tümü")
  const [searchTerm, setSearchTerm] = useState("")
  const [paramsProcessed, setParamsProcessed] = useState(false)
  const [editingId, setEditingId] = useState<number | null>(null)
  const [editForm, setEditForm] = useState<Islem | null>(null)

  // URL'den gelen ürün bilgilerini form alanlarına doldur
  useEffect(() => {
    if (paramsProcessed || !searchParams) return

    const urunId = searchParams.get("urunId")
    if (urunId) {
      const id = Number.parseInt(urunId)
      const urun = urunler.find((u) => u.id === id)

      if (urun) {
        setNewTransaction({
          tarih: new Date(),
          kategori: urun.kategori,
          urunId: urun.id,
          urunAdi: urun.ad,
          alisFiyati: urun.alisFiyati,
          satisFiyati: urun.satisFiyati,
          musteriAdi: "",
        })
        setParamsProcessed(true)
      }
    }
  }, [searchParams, urunler, paramsProcessed])

  // İşlemleri filtrele
  const filteredIslemler = islemler.filter((islem) => {
    // Tarih filtresi
    const tarihUygun =
      (!filterStartDate || islem.tarih >= filterStartDate) && (!filterEndDate || islem.tarih <= filterEndDate)

    // Kategori filtresi
    const kategoriUygun = filterCategory === "Tümü" || islem.kategori === filterCategory

    // Arama filtresi
    const aramaUygun =
      !searchTerm ||
      islem.urunAdi.toLowerCase().includes(searchTerm.toLowerCase()) ||
      islem.musteriAdi.toLowerCase().includes(searchTerm.toLowerCase())

    return tarihUygun && kategoriUygun && aramaUygun
  })

  // Toplam değerler
  const toplamKar = filteredIslemler.reduce((sum, islem) => sum + islem.kar, 0)
  const haftalikKar = islemler
    .filter((islem) => {
      const birHaftaOnce = new Date()
      birHaftaOnce.setDate(birHaftaOnce.getDate() - 7)
      return islem.tarih >= birHaftaOnce
    })
    .reduce((sum, islem) => sum + islem.kar, 0)

  const aylikKar = islemler
    .filter((islem) => {
      const birAyOnce = new Date()
      birAyOnce.setMonth(birAyOnce.getMonth() - 1)
      return islem.tarih >= birAyOnce
    })
    .reduce((sum, islem) => sum + islem.kar, 0)

  const handleSave = () => {
    // Validasyon
    if (!newTransaction.urunAdi) {
      toast({
        title: "Hata",
        description: "Lütfen bir ürün seçin veya ürün adı girin",
        variant: "destructive",
      })
      return
    }

    if (newTransaction.satisFiyati <= 0) {
      toast({
        title: "Hata",
        description: "Satış fiyatı sıfırdan büyük olmalıdır",
        variant: "destructive",
      })
      return
    }

    // Kar hesapla
    const kar = newTransaction.satisFiyati - newTransaction.alisFiyati

    // Yeni işlem oluştur
    const yeniIslem: Omit<Islem, "id"> = {
      tarih: newTransaction.tarih,
      kategori: newTransaction.kategori,
      urunId: newTransaction.urunId,
      urunAdi: newTransaction.urunAdi,
      alisFiyati: newTransaction.alisFiyati,
      satisFiyati: newTransaction.satisFiyati,
      kar: kar,
      musteriAdi: newTransaction.musteriAdi,
    }

    // İşlemi ekle
    islemEkle(yeniIslem)

    // Stoktan düş (eğer ürün ID'si varsa)
    if (newTransaction.urunId > 0) {
      const stokGuncellendi = stokGuncelle(newTransaction.urunId, -1)
      if (!stokGuncellendi) {
        toast({
          title: "Uyarı",
          description: "Stok yetersiz! İşlem kaydedildi ancak stok güncellenemedi.",
          variant: "destructive",
        })
      }
    }

    toast({
      title: "Başarılı",
      description: "İşlem başarıyla kaydedildi",
    })

    // Formu temizle
    handleClear()

    // Filtreleri sıfırla ve tüm işlemleri göster
    setFilterStartDate(new Date(new Date().setDate(new Date().getDate() - 7)))
    setFilterEndDate(new Date())
    setFilterCategory("Tümü")
    setSearchTerm("")
  }

  const handleClear = () => {
    setNewTransaction({
      tarih: new Date(),
      kategori: "Telefon",
      urunId: 0,
      urunAdi: "",
      alisFiyati: 0,
      satisFiyati: 0,
      musteriAdi: "",
    })
    setParamsProcessed(false)
  }

  const handleDelete = (id: number) => {
    const success = islemSil(id)
    if (success) {
      toast({
        title: "Başarılı",
        description: "İşlem başarıyla silindi",
      })
    } else {
      toast({
        title: "Hata",
        description: "İşlem silinirken bir hata oluştu",
        variant: "destructive",
      })
    }
  }

  const handleEdit = (islem: Islem) => {
    setEditingId(islem.id)
    setEditForm({ ...islem })
  }

  const handleSaveEdit = () => {
    if (!editForm) return

    // Kar hesapla
    const kar = editForm.satisFiyati - editForm.alisFiyati

    // İşlemi güncelle
    const success = islemGuncelle(editForm.id, { ...editForm, kar })
    if (success) {
      toast({
        title: "Başarılı",
        description: "İşlem başarıyla güncellendi",
      })
      setEditingId(null)
      setEditForm(null)
    } else {
      toast({
        title: "Hata",
        description: "İşlem güncellenirken bir hata oluştu",
        variant: "destructive",
      })
    }
  }

  const handleCancelEdit = () => {
    setEditingId(null)
    setEditForm(null)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Toaster />
      <div className="container mx-auto p-4">
        <header className="flex justify-between items-center mb-6">
          <div className="w-1/3"></div>
          <h1 className="text-2xl font-bold text-center w-1/3">Telefoncu İşlem Takip Sistemi</h1>
          <div className="w-1/3 text-right">
            <div className="mb-2">
              <span className="font-semibold">Haftalık Toplam Kar: </span>
              <span>₺{haftalikKar.toLocaleString()}</span>
            </div>
            <div className="mb-2">
              <span className="font-semibold">Aylık Toplam Kar: </span>
              <span>₺{aylikKar.toLocaleString()}</span>
            </div>
            <Link href="/dashboard">
              <Button variant="outline" className="w-full mb-2">
                Yönetim Paneli
              </Button>
            </Link>
            <Link href="/urunler">
              <Button variant="outline" className="w-full">
                Ürün Yönetimi
              </Button>
            </Link>
          </div>
        </header>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="md:col-span-1">
            <CardContent className="p-6">
              <h2 className="text-lg font-semibold mb-4 border-b pb-2">Yeni İşlem Ekle</h2>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm mb-1">Tarih:</label>
                  <div className="flex">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="outline" className="w-full justify-start text-left font-normal">
                          {newTransaction.tarih
                            ? format(newTransaction.tarih, "dd MMMM yyyy", { locale: tr })
                            : "Tarih Seçin"}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={newTransaction.tarih}
                          onSelect={(date) => setNewTransaction({ ...newTransaction, tarih: date || new Date() })}
                          locale={tr}
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                <div>
                  <label className="block text-sm mb-1">Kategori:</label>
                  <Select
                    value={newTransaction.kategori}
                    onValueChange={(value) => setNewTransaction({ ...newTransaction, kategori: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Kategori Seçin" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Telefon">Telefon</SelectItem>
                      <SelectItem value="Aksesuar">Aksesuar</SelectItem>
                      <SelectItem value="Tamir">Tamir</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex space-x-2">
                  <Link href="/urunler/aksesuar">
                    <Button variant="outline" size="sm">
                      Aksesuar Seç
                    </Button>
                  </Link>
                  <Link href="/urunler/telefon">
                    <Button variant="outline" size="sm">
                      Telefon Seç
                    </Button>
                  </Link>
                </div>

                <div>
                  <label className="block text-sm mb-1">Ürün/Hizmet:</label>
                  <Input
                    value={newTransaction.urunAdi}
                    onChange={(e) => setNewTransaction({ ...newTransaction, urunAdi: e.target.value })}
                  />
                </div>

                <div>
                  <label className="block text-sm mb-1">Maliyet (₺):</label>
                  <Input
                    type="number"
                    value={newTransaction.alisFiyati || ""}
                    onChange={(e) =>
                      setNewTransaction({ ...newTransaction, alisFiyati: Number.parseFloat(e.target.value) || 0 })
                    }
                  />
                </div>

                <div>
                  <label className="block text-sm mb-1">Satış Fiyatı (₺):</label>
                  <Input
                    type="number"
                    value={newTransaction.satisFiyati || ""}
                    onChange={(e) =>
                      setNewTransaction({ ...newTransaction, satisFiyati: Number.parseFloat(e.target.value) || 0 })
                    }
                  />
                </div>

                <div>
                  <label className="block text-sm mb-1">Müşteri Adı (Opsiyonel):</label>
                  <Input
                    value={newTransaction.musteriAdi}
                    onChange={(e) => setNewTransaction({ ...newTransaction, musteriAdi: e.target.value })}
                  />
                </div>

                <div className="flex space-x-2 pt-2">
                  <Button onClick={handleSave}>Kaydet</Button>
                  <Button variant="outline" onClick={handleClear}>
                    Formu Temizle
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="md:col-span-2">
            <CardContent className="p-6">
              <div className="mb-6">
                <h2 className="text-lg font-semibold mb-4 border-b pb-2">Filtreler</h2>
                <div className="flex flex-wrap items-center gap-2">
                  <span>Tarih Aralığı:</span>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="justify-start text-left font-normal">
                        {filterStartDate ? format(filterStartDate, "dd MMMM yyyy", { locale: tr }) : "Başlangıç"}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar mode="single" selected={filterStartDate} onSelect={setFilterStartDate} locale={tr} />
                    </PopoverContent>
                  </Popover>
                  <span>-</span>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="justify-start text-left font-normal">
                        {filterEndDate ? format(filterEndDate, "dd MMMM yyyy", { locale: tr }) : "Bitiş"}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar mode="single" selected={filterEndDate} onSelect={setFilterEndDate} locale={tr} />
                    </PopoverContent>
                  </Popover>

                  <span className="ml-4">Kategori:</span>
                  <Select value={filterCategory} onValueChange={setFilterCategory}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Kategori Seçin" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Tümü">Tümü</SelectItem>
                      <SelectItem value="Telefon">Telefon</SelectItem>
                      <SelectItem value="Aksesuar">Aksesuar</SelectItem>
                      <SelectItem value="Tamir">Tamir</SelectItem>
                    </SelectContent>
                  </Select>

                  <div className="relative ml-4 w-[180px]">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      className="pl-8"
                      placeholder="Ara..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold">İşlem Listesi</h2>
                <Badge variant="outline" className="text-green-600">
                  Toplam Kar: ₺{toplamKar.toLocaleString()}
                </Badge>
              </div>

              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ID</TableHead>
                      <TableHead>Tarih</TableHead>
                      <TableHead>Kategori</TableHead>
                      <TableHead>Ürün/Hizmet</TableHead>
                      <TableHead>Maliyet (₺)</TableHead>
                      <TableHead>Satış Fiyatı (₺)</TableHead>
                      <TableHead>Kar (₺)</TableHead>
                      <TableHead>Müşteri Adı</TableHead>
                      <TableHead>İşlemler</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredIslemler.length > 0 ? (
                      filteredIslemler.map((islem) => (
                        <TableRow key={islem.id}>
                          <TableCell>{islem.id}</TableCell>
                          <TableCell>
                            {editingId === islem.id ? (
                              <Popover>
                                <PopoverTrigger asChild>
                                  <Button variant="outline" className="w-full justify-start text-left font-normal">
                                    {editForm?.tarih
                                      ? format(editForm.tarih, "dd MMMM yyyy", { locale: tr })
                                      : "Tarih Seçin"}
                                    <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                  </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0" align="start">
                                  <Calendar
                                    mode="single"
                                    selected={editForm?.tarih}
                                    onSelect={(date) => setEditForm({ ...editForm!, tarih: date || new Date() })}
                                    locale={tr}
                                  />
                                </PopoverContent>
                              </Popover>
                            ) : (
                              format(islem.tarih, "dd.MM.yyyy")
                            )}
                          </TableCell>
                          <TableCell>
                            {editingId === islem.id ? (
                              <Select
                                value={editForm?.kategori}
                                onValueChange={(value) => setEditForm({ ...editForm!, kategori: value })}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Kategori Seçin" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="Telefon">Telefon</SelectItem>
                                  <SelectItem value="Aksesuar">Aksesuar</SelectItem>
                                  <SelectItem value="Tamir">Tamir</SelectItem>
                                </SelectContent>
                              </Select>
                            ) : (
                              islem.kategori
                            )}
                          </TableCell>
                          <TableCell>
                            {editingId === islem.id ? (
                              <Input
                                value={editForm?.urunAdi}
                                onChange={(e) => setEditForm({ ...editForm!, urunAdi: e.target.value })}
                              />
                            ) : (
                              islem.urunAdi
                            )}
                          </TableCell>
                          <TableCell>
                            {editingId === islem.id ? (
                              <Input
                                type="number"
                                value={editForm?.alisFiyati}
                                onChange={(e) =>
                                  setEditForm({ ...editForm!, alisFiyati: Number.parseFloat(e.target.value) || 0 })
                                }
                              />
                            ) : (
                              islem.alisFiyati.toLocaleString()
                            )}
                          </TableCell>
                          <TableCell>
                            {editingId === islem.id ? (
                              <Input
                                type="number"
                                value={editForm?.satisFiyati}
                                onChange={(e) =>
                                  setEditForm({ ...editForm!, satisFiyati: Number.parseFloat(e.target.value) || 0 })
                                }
                              />
                            ) : (
                              islem.satisFiyati.toLocaleString()
                            )}
                          </TableCell>
                          <TableCell className="font-medium text-green-600">
                            {editingId === islem.id
                              ? (editForm!.satisFiyati - editForm!.alisFiyati).toLocaleString()
                              : islem.kar.toLocaleString()}
                          </TableCell>
                          <TableCell>
                            {editingId === islem.id ? (
                              <Input
                                value={editForm?.musteriAdi}
                                onChange={(e) => setEditForm({ ...editForm!, musteriAdi: e.target.value })}
                              />
                            ) : (
                              islem.musteriAdi
                            )}
                          </TableCell>
                          <TableCell>
                            {editingId === islem.id ? (
                              <div className="flex space-x-1">
                                <Button size="sm" onClick={handleSaveEdit}>
                                  <Save className="h-4 w-4" />
                                </Button>
                                <Button size="sm" variant="outline" onClick={handleCancelEdit}>
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                            ) : (
                              <div className="flex space-x-1">
                                <Button size="sm" variant="outline" onClick={() => handleEdit(islem)}>
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <AlertDialog>
                                  <AlertDialogTrigger asChild>
                                    <Button size="sm" variant="outline" className="text-red-500">
                                      <Trash className="h-4 w-4" />
                                    </Button>
                                  </AlertDialogTrigger>
                                  <AlertDialogContent>
                                    <AlertDialogHeader>
                                      <AlertDialogTitle>İşlemi Sil</AlertDialogTitle>
                                      <AlertDialogDescription>
                                        Bu işlemi silmek istediğinizden emin misiniz? Bu işlem geri alınamaz ve stok
                                        miktarı otomatik olarak güncellenecektir.
                                      </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                      <AlertDialogCancel>İptal</AlertDialogCancel>
                                      <AlertDialogAction onClick={() => handleDelete(islem.id)}>
                                        Evet, Sil
                                      </AlertDialogAction>
                                    </AlertDialogFooter>
                                  </AlertDialogContent>
                                </AlertDialog>
                              </div>
                            )}
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={9} className="text-center py-4 text-muted-foreground">
                          Kayıtlı işlem bulunamadı
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
