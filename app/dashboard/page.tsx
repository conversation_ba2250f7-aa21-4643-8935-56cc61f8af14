"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Stok<PERSON><PERSON><PERSON>, <PERSON>tis<PERSON>ti, SonIslemler, EnCokSatanlar } from "@/components/dashboard-widgets"
import { ArrowLeft, PieChart } from "lucide-react"
import Link from "next/link"
import { useIslemler } from "@/lib/db"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from "chart.js"
import { Line, Bar, Pie } from "react-chartjs-2"

// Chart.js bileşenlerini kaydet
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend, ArcElement)

export default function DashboardPage() {
  const { islemler } = useIslemler()

  // Son 7 gün<PERSON><PERSON><PERSON> satış verileri
  const son7Gun = Array.from({ length: 7 }, (_, i) => {
    const date = new Date()
    date.setDate(date.getDate() - (6 - i))
    return {
      tarih: date,
      tarihStr: date.toLocaleDateString("tr-TR", { day: "2-digit", month: "2-digit" }),
    }
  })

  const gunlukSatisVerileri = son7Gun.map((gun) => {
    const gunIslemleri = islemler.filter((islem) => {
      const islemTarih = new Date(islem.tarih)
      return (
        islemTarih.getDate() === gun.tarih.getDate() &&
        islemTarih.getMonth() === gun.tarih.getMonth() &&
        islemTarih.getFullYear() === gun.tarih.getFullYear()
      )
    })

    return {
      tarih: gun.tarihStr,
      ciro: gunIslemleri.reduce((sum, islem) => sum + islem.satisFiyati, 0),
      kar: gunIslemleri.reduce((sum, islem) => sum + islem.kar, 0),
      adet: gunIslemleri.length,
    }
  })

  // Kategori bazlı satış verileri
  const kategoriBazliVeriler = islemler.reduce(
    (acc, islem) => {
      if (!acc[islem.kategori]) {
        acc[islem.kategori] = {
          adet: 0,
          ciro: 0,
          kar: 0,
        }
      }

      acc[islem.kategori].adet += 1
      acc[islem.kategori].ciro += islem.satisFiyati
      acc[islem.kategori].kar += islem.kar

      return acc
    },
    {} as Record<string, { adet: number; ciro: number; kar: number }>,
  )

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center mb-6">
        <Link href="/">
          <Button variant="outline" size="sm" className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" /> Ana Sayfa
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">Yönetim Paneli</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <StokDurumu />
        <SatisOzeti />
        <div className="md:col-span-2">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Haftalık Satış Grafiği</CardTitle>
            </CardHeader>
            <CardContent className="p-4">
              <Line
                data={{
                  labels: gunlukSatisVerileri.map((d) => d.tarih),
                  datasets: [
                    {
                      label: "Ciro",
                      data: gunlukSatisVerileri.map((d) => d.ciro),
                      borderColor: "rgb(53, 162, 235)",
                      backgroundColor: "rgba(53, 162, 235, 0.5)",
                    },
                    {
                      label: "Kar",
                      data: gunlukSatisVerileri.map((d) => d.kar),
                      borderColor: "rgb(75, 192, 192)",
                      backgroundColor: "rgba(75, 192, 192, 0.5)",
                    },
                  ],
                }}
                options={{
                  responsive: true,
                  plugins: {
                    legend: {
                      position: "top",
                    },
                    title: {
                      display: false,
                    },
                  },
                  scales: {
                    y: {
                      beginAtZero: true,
                    },
                  },
                }}
              />
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <SonIslemler />
        <EnCokSatanlar />
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Kategori Dağılımı</CardTitle>
          </CardHeader>
          <CardContent className="p-4">
            {Object.keys(kategoriBazliVeriler).length > 0 ? (
              <Pie
                data={{
                  labels: Object.keys(kategoriBazliVeriler),
                  datasets: [
                    {
                      label: "Satış Adedi",
                      data: Object.values(kategoriBazliVeriler).map((k) => k.adet),
                      backgroundColor: [
                        "rgba(54, 162, 235, 0.6)",
                        "rgba(255, 99, 132, 0.6)",
                        "rgba(75, 192, 192, 0.6)",
                      ],
                      borderColor: ["rgba(54, 162, 235, 1)", "rgba(255, 99, 132, 1)", "rgba(75, 192, 192, 1)"],
                      borderWidth: 1,
                    },
                  ],
                }}
                options={{
                  responsive: true,
                  plugins: {
                    legend: {
                      position: "bottom",
                    },
                  },
                }}
              />
            ) : (
              <div className="flex flex-col items-center py-8">
                <PieChart className="h-24 w-24 text-muted-foreground" />
                <p className="text-sm text-muted-foreground mt-4">Veri bulunamadı</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="satislar">
        <TabsList className="mb-4">
          <TabsTrigger value="satislar">Satış Analizi</TabsTrigger>
          <TabsTrigger value="urunler">Ürün Analizi</TabsTrigger>
          <TabsTrigger value="karlilik">Karlılık Analizi</TabsTrigger>
        </TabsList>

        <TabsContent value="satislar">
          <Card>
            <CardHeader>
              <CardTitle>Satış Analizi</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <Bar
                  data={{
                    labels: gunlukSatisVerileri.map((d) => d.tarih),
                    datasets: [
                      {
                        label: "Satış Adedi",
                        data: gunlukSatisVerileri.map((d) => d.adet),
                        backgroundColor: "rgba(255, 99, 132, 0.6)",
                      },
                      {
                        label: "Ciro (₺100)",
                        data: gunlukSatisVerileri.map((d) => d.ciro / 100),
                        backgroundColor: "rgba(54, 162, 235, 0.6)",
                      },
                    ],
                  }}
                  options={{
                    responsive: true,
                    plugins: {
                      legend: {
                        position: "top",
                      },
                      title: {
                        display: true,
                        text: "Günlük Satış Verileri",
                      },
                    },
                    scales: {
                      y: {
                        beginAtZero: true,
                      },
                    },
                  }}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="urunler">
          <Card>
            <CardHeader>
              <CardTitle>Ürün Analizi</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <Pie
                  data={{
                    labels: Object.keys(kategoriBazliVeriler),
                    datasets: [
                      {
                        label: "Ciro",
                        data: Object.values(kategoriBazliVeriler).map((k) => k.ciro),
                        backgroundColor: [
                          "rgba(54, 162, 235, 0.6)",
                          "rgba(255, 99, 132, 0.6)",
                          "rgba(75, 192, 192, 0.6)",
                        ],
                        borderColor: ["rgba(54, 162, 235, 1)", "rgba(255, 99, 132, 1)", "rgba(75, 192, 192, 1)"],
                        borderWidth: 1,
                      },
                    ],
                  }}
                  options={{
                    responsive: true,
                    plugins: {
                      legend: {
                        position: "top",
                      },
                      title: {
                        display: true,
                        text: "Kategori Bazlı Ciro Dağılımı",
                      },
                    },
                  }}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="karlilik">
          <Card>
            <CardHeader>
              <CardTitle>Karlılık Analizi</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <Bar
                  data={{
                    labels: Object.keys(kategoriBazliVeriler),
                    datasets: [
                      {
                        label: "Kar",
                        data: Object.values(kategoriBazliVeriler).map((k) => k.kar),
                        backgroundColor: "rgba(75, 192, 192, 0.6)",
                      },
                      {
                        label: "Kar Marjı (%)",
                        data: Object.values(kategoriBazliVeriler).map((k) => (k.ciro > 0 ? (k.kar / k.ciro) * 100 : 0)),
                        backgroundColor: "rgba(153, 102, 255, 0.6)",
                      },
                    ],
                  }}
                  options={{
                    responsive: true,
                    plugins: {
                      legend: {
                        position: "top",
                      },
                      title: {
                        display: true,
                        text: "Kategori Bazlı Karlılık Analizi",
                      },
                    },
                    scales: {
                      y: {
                        beginAtZero: true,
                      },
                    },
                  }}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
