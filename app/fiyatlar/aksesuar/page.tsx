"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ArrowLeft, Edit, Plus, Save, Search, Trash, ShoppingCart } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { useRouter } from "next/navigation"

interface Aksesuar {
  id: number
  ad: string
  resimUrl: string
  alisFiyati: number
  satisFiyati: number
  stok: number
  kategori: string
}

export default function AksesuarFiyatlari() {
  const router = useRouter()

  const [aksesuarlar, setAksesuarlar] = useState<Aksesuar[]>([
    {
      id: 1,
      ad: "iPhone 13 Silikon Kılıf",
      resimUrl: "/placeholder.svg?height=80&width=80",
      alisFiyati: 50,
      satisFiyati: 150,
      stok: 25,
      kategori: "Kılıf",
    },
    {
      id: 2,
      ad: "Samsung S22 Şeffaf Kılıf",
      resimUrl: "/placeholder.svg?height=80&width=80",
      alisFiyati: 40,
      satisFiyati: 120,
      stok: 15,
      kategori: "Kılıf",
    },
    {
      id: 3,
      ad: "Type-C Şarj Kablosu",
      resimUrl: "/placeholder.svg?height=80&width=80",
      alisFiyati: 30,
      satisFiyati: 90,
      stok: 20,
      kategori: "Şarj Aleti",
    },
    {
      id: 4,
      ad: "iPhone Lightning Şarj Kablosu",
      resimUrl: "/placeholder.svg?height=80&width=80",
      alisFiyati: 35,
      satisFiyati: 100,
      stok: 18,
      kategori: "Şarj Aleti",
    },
    {
      id: 5,
      ad: "Bluetooth Kulaklık",
      resimUrl: "/placeholder.svg?height=80&width=80",
      alisFiyati: 150,
      satisFiyati: 300,
      stok: 10,
      kategori: "Kulaklık",
    },
    {
      id: 6,
      ad: "Temperli Cam Ekran Koruyucu",
      resimUrl: "/placeholder.svg?height=80&width=80",
      alisFiyati: 20,
      satisFiyati: 80,
      stok: 30,
      kategori: "Ekran Koruyucu",
    },
  ])

  const [searchTerm, setSearchTerm] = useState("")
  const [editingId, setEditingId] = useState<number | null>(null)
  const [editForm, setEditForm] = useState<Aksesuar | null>(null)
  const [selectedCategory, setSelectedCategory] = useState<string>("Tümü")

  const filteredAksesuarlar = aksesuarlar.filter(
    (aksesuar) =>
      (selectedCategory === "Tümü" || aksesuar.kategori === selectedCategory) &&
      (aksesuar.ad.toLowerCase().includes(searchTerm.toLowerCase()) ||
        aksesuar.kategori.toLowerCase().includes(searchTerm.toLowerCase())),
  )

  const handleEdit = (aksesuar: Aksesuar) => {
    setEditingId(aksesuar.id)
    setEditForm({ ...aksesuar })
  }

  const handleSave = () => {
    if (!editForm) return

    setAksesuarlar(aksesuarlar.map((aksesuar) => (aksesuar.id === editingId ? { ...editForm } : aksesuar)))
    setEditingId(null)
    setEditForm(null)
  }

  const handleCancel = () => {
    setEditingId(null)
    setEditForm(null)
  }

  const handleDelete = (id: number) => {
    setAksesuarlar(aksesuarlar.filter((aksesuar) => aksesuar.id !== id))
  }

  const handleSelectAksesuar = (aksesuar: Aksesuar) => {
    // Aksesuar bilgilerini URL parametresi olarak gönder
    const params = new URLSearchParams({
      product: aksesuar.ad,
      cost: aksesuar.alisFiyati.toString(),
      price: aksesuar.satisFiyati.toString(),
      category: "Aksesuar",
    })

    router.push(`/?${params.toString()}`)
  }

  const categories = ["Tümü", "Kılıf", "Şarj Aleti", "Kulaklık", "Ekran Koruyucu"]

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center mb-6">
        <Link href="/fiyatlar">
          <Button variant="outline" size="sm" className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" /> Geri
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">Aksesuar Fiyatları</h1>
      </div>

      <div className="flex flex-wrap gap-4 mb-6">
        <div className="relative w-full max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Aksesuar ara..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              onClick={() => setSelectedCategory(category)}
              size="sm"
            >
              {category}
            </Button>
          ))}
        </div>

        <Button className="ml-auto">
          <Plus className="h-4 w-4 mr-2" /> Yeni Aksesuar Ekle
        </Button>
      </div>

      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Görsel</TableHead>
                  <TableHead>Aksesuar Adı</TableHead>
                  <TableHead>Kategori</TableHead>
                  <TableHead>Alış Fiyatı (₺)</TableHead>
                  <TableHead>Satış Fiyatı (₺)</TableHead>
                  <TableHead>Stok</TableHead>
                  <TableHead>İşlemler</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAksesuarlar.map((aksesuar) => (
                  <TableRow key={aksesuar.id}>
                    <TableCell>
                      <Image
                        src={aksesuar.resimUrl || "/placeholder.svg"}
                        alt={aksesuar.ad}
                        width={50}
                        height={50}
                        className="rounded-md object-cover"
                      />
                    </TableCell>
                    <TableCell>
                      {editingId === aksesuar.id ? (
                        <Input
                          value={editForm?.ad}
                          onChange={(e) => setEditForm({ ...editForm!, ad: e.target.value })}
                        />
                      ) : (
                        aksesuar.ad
                      )}
                    </TableCell>
                    <TableCell>
                      {editingId === aksesuar.id ? (
                        <Input
                          value={editForm?.kategori}
                          onChange={(e) => setEditForm({ ...editForm!, kategori: e.target.value })}
                        />
                      ) : (
                        aksesuar.kategori
                      )}
                    </TableCell>
                    <TableCell>
                      {editingId === aksesuar.id ? (
                        <Input
                          type="number"
                          value={editForm?.alisFiyati}
                          onChange={(e) => setEditForm({ ...editForm!, alisFiyati: Number(e.target.value) })}
                        />
                      ) : (
                        aksesuar.alisFiyati
                      )}
                    </TableCell>
                    <TableCell>
                      {editingId === aksesuar.id ? (
                        <Input
                          type="number"
                          value={editForm?.satisFiyati}
                          onChange={(e) => setEditForm({ ...editForm!, satisFiyati: Number(e.target.value) })}
                        />
                      ) : (
                        aksesuar.satisFiyati
                      )}
                    </TableCell>
                    <TableCell>
                      {editingId === aksesuar.id ? (
                        <Input
                          type="number"
                          value={editForm?.stok}
                          onChange={(e) => setEditForm({ ...editForm!, stok: Number(e.target.value) })}
                        />
                      ) : (
                        aksesuar.stok
                      )}
                    </TableCell>
                    <TableCell>
                      {editingId === aksesuar.id ? (
                        <div className="flex space-x-2">
                          <Button size="sm" onClick={handleSave}>
                            <Save className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline" onClick={handleCancel}>
                            İptal
                          </Button>
                        </div>
                      ) : (
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="default"
                            className="bg-green-600 hover:bg-green-700"
                            onClick={() => handleSelectAksesuar(aksesuar)}
                          >
                            <ShoppingCart className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => handleEdit(aksesuar)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            className="text-red-500"
                            onClick={() => handleDelete(aksesuar.id)}
                          >
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
