"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowLeft, Smartphone, Zap } from "lucide-react"
import Link from "next/link"
import Image from "next/image"

export default function FiyatlarPage() {
  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center mb-6">
        <Link href="/">
          <Button variant="outline" size="sm" className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" /> Ana Sayfa
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">Fiyat Düzenleme</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Link href="/fiyatlar/telefon">
          <Card className="cursor-pointer hover:bg-gray-50 transition-colors">
            <CardHeader className="flex flex-row items-center gap-4">
              <div className="bg-blue-100 p-3 rounded-full">
                <Smartphone className="h-8 w-8 text-blue-700" />
              </div>
              <div>
                <CardTitle>Telefon Fiyatları</CardTitle>
                <CardDescription>Telefon modellerinin fiyatlarını düzenleyin</CardDescription>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4">
                <div className="flex flex-col items-center">
                  <Image
                    src="/placeholder.svg?height=80&width=80"
                    alt="iPhone"
                    width={80}
                    height={80}
                    className="rounded-md mb-2"
                  />
                  <span className="text-sm font-medium">iPhone 13</span>
                  <span className="text-sm text-muted-foreground">₺22.000</span>
                </div>
                <div className="flex flex-col items-center">
                  <Image
                    src="/placeholder.svg?height=80&width=80"
                    alt="Samsung"
                    width={80}
                    height={80}
                    className="rounded-md mb-2"
                  />
                  <span className="text-sm font-medium">Galaxy S22</span>
                  <span className="text-sm text-muted-foreground">₺18.500</span>
                </div>
                <div className="flex flex-col items-center">
                  <Image
                    src="/placeholder.svg?height=80&width=80"
                    alt="Xiaomi"
                    width={80}
                    height={80}
                    className="rounded-md mb-2"
                  />
                  <span className="text-sm font-medium">Redmi Note 11</span>
                  <span className="text-sm text-muted-foreground">₺7.500</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href="/fiyatlar/aksesuar">
          <Card className="cursor-pointer hover:bg-gray-50 transition-colors">
            <CardHeader className="flex flex-row items-center gap-4">
              <div className="bg-green-100 p-3 rounded-full">
                <Zap className="h-8 w-8 text-green-700" />
              </div>
              <div>
                <CardTitle>Aksesuar Fiyatları</CardTitle>
                <CardDescription>Kılıf, şarj aleti ve diğer aksesuarların fiyatlarını düzenleyin</CardDescription>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4">
                <div className="flex flex-col items-center">
                  <Image
                    src="/placeholder.svg?height=80&width=80"
                    alt="Kılıf"
                    width={80}
                    height={80}
                    className="rounded-md mb-2"
                  />
                  <span className="text-sm font-medium">Silikon Kılıf</span>
                  <span className="text-sm text-muted-foreground">₺150</span>
                </div>
                <div className="flex flex-col items-center">
                  <Image
                    src="/placeholder.svg?height=80&width=80"
                    alt="Şarj Aleti"
                    width={80}
                    height={80}
                    className="rounded-md mb-2"
                  />
                  <span className="text-sm font-medium">Type-C Şarj</span>
                  <span className="text-sm text-muted-foreground">₺90</span>
                </div>
                <div className="flex flex-col items-center">
                  <Image
                    src="/placeholder.svg?height=80&width=80"
                    alt="Kulaklık"
                    width={80}
                    height={80}
                    className="rounded-md mb-2"
                  />
                  <span className="text-sm font-medium">Bluetooth Kulaklık</span>
                  <span className="text-sm text-muted-foreground">₺300</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </Link>
      </div>
    </div>
  )
}
