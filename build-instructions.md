# Windows EXE Oluşturma Kılavuzu

## 🎯 Windows için EXE Dosyası Oluşturma

### 1. Gereksinimler
- Node.js 18+
- npm veya pnpm
- Windows 10/11 (cross-platform build için)

### 2. Build Komutları

#### Windows Installer (.exe) Oluşturma:
```bash
npm run dist:win
```

#### Portable EXE Oluşturma (kurulum gerektirmez):
```bash
npm run dist:win-portable
```

#### Tüm Platformlar için Build:
```bash
npm run dist:all
```

### 3. Oluşturulan Dosyalar

Build işlemi tamamlandıktan sonra `dist/` klasöründe şu dosyalar oluşur:

#### NSIS Installer:
- `Telefoncu Takip Sistemi Setup 0.1.0.exe` - 64-bit installer
- `Telefoncu Takip Sistemi Setup 0.1.0-ia32.exe` - 32-bit installer

#### Portable Sürüm:
- `Telefoncu Takip Sistemi 0.1.0.exe` - 64-bit portable
- `Telefoncu Takip Sistemi 0.1.0-ia32.exe` - 32-bit portable

### 4. Installer Özellikleri

- **Kurulum Dizini Seçimi**: Kullanıcı istediği klasöre kurabilir
- **Masaüstü Kısayolu**: Otomatik oluşturulur
- **Başlat Menüsü**: Kısayol eklenir
- **Kaldırma**: Windows'un "Programlar ve Özellikler" bölümünden

### 5. Portable Sürüm Özellikleri

- **Kurulum Gerektirmez**: Tek dosya olarak çalışır
- **Taşınabilir**: USB'de veya herhangi bir klasörde çalışır
- **Kayıt Defteri Kullanmaz**: Sistem temiz kalır

### 6. Sistem Gereksinimleri

- **İşletim Sistemi**: Windows 7/8/10/11
- **Mimari**: x64 (64-bit) veya x86 (32-bit)
- **RAM**: Minimum 4GB önerilir
- **Disk Alanı**: ~200MB

### 7. Sorun Giderme

#### Build Hatası:
```bash
# Bağımlılıkları temizle ve yeniden yükle
rm -rf node_modules
npm install --legacy-peer-deps
```

#### Windows Defender Uyarısı:
- İmzasız uygulama uyarısı normal
- "Daha fazla bilgi" → "Yine de çalıştır" seçin
- Güvenlik için kendi sertifikanızla imzalayabilirsiniz

#### Antivirus Uyarısı:
- Electron uygulamaları bazen false positive verir
- Antivirus'e istisna ekleyin

### 8. Dağıtım

#### Kullanıcılara Dağıtım:
1. `dist/` klasöründeki .exe dosyasını paylaşın
2. Installer sürümü önerilir (daha profesyonel)
3. Portable sürüm test için idealdir

#### Güvenlik:
- Dosyaları güvenilir kaynaklardan indirin
- SHA256 hash kontrolü yapın
- Dijital imza ekleyin (opsiyonel)

### 9. Güncelleme

Yeni sürüm için:
1. `package.json`'da version numarasını artırın
2. `npm run dist:win` komutunu çalıştırın
3. Yeni .exe dosyasını dağıtın

### 10. Cross-Platform Build

macOS'ta Windows için build yapmak:
```bash
# Wine gerekebilir
brew install wine

# Build
npm run dist:win
```

Linux'ta Windows için build yapmak:
```bash
# Wine kurulumu
sudo apt install wine

# Build
npm run dist:win
```

## 📝 Notlar

- İlk build uzun sürebilir (Electron binary indirme)
- Sonraki build'ler daha hızlı olur
- İnternet bağlantısı gereklidir (ilk build için)
- Dosya boyutu ~150-200MB olabilir (Electron runtime dahil)

## 🚀 Hızlı Başlangıç

```bash
# 1. Projeyi hazırla
npm install --legacy-peer-deps

# 2. Windows EXE oluştur
npm run dist:win

# 3. dist/ klasöründeki .exe dosyasını çalıştır
```
