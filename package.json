{"name": "telefoncu-takip-electron", "version": "0.1.0", "private": true, "main": "electron/main.js", "homepage": "./", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron .\"", "build:electron": "ELECTRON_BUILD=1 npm run build", "rebuild": "npx electron-rebuild", "postinstall": "npx electron-rebuild", "db:path": "node scripts/db-path.js path", "db:info": "node scripts/db-manager.js info", "db:stats": "node scripts/db-manager.js stats", "db:shell": "node scripts/db-manager.js shell", "db:backup": "node scripts/db-manager.js backup", "db:scripts": "node scripts/db-manager.js scripts", "db:commands": "node scripts/db-path.js commands", "db:manager": "node scripts/db-manager.js", "electron-build": "npm run build:electron && electron-builder --win --x64 --publish=never", "dist": "npm run build:electron && electron-builder --win --x64 --publish=never", "dist:win-x64": "npm run build:electron && electron-builder --win --x64 --publish=never", "dist:win-portable": "npm run build:electron && electron-builder --win --x64 --config.win.target=portable --publish=never"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@types/better-sqlite3": "^7.6.13", "autoprefixer": "^10.4.20", "better-sqlite3": "^11.10.0", "chart.js": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "latest", "embla-carousel-react": "8.5.1", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "^0.4.4", "react": "^19", "react-chartjs-2": "latest", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^9.0.0", "electron": "^32.0.0", "electron-builder": "^25.0.0", "electron-rebuild": "^3.2.9", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5", "wait-on": "^8.0.0"}, "build": {"appId": "com.telefoncu.takip", "productName": "Telefoncu Ta<PERSON>mi", "directories": {"output": "dist"}, "files": ["out/**/*", "electron/**/*", "scripts/**/*", "node_modules/**/*", "!node_modules/.cache/**/*"], "extraResources": [{"from": "scripts/", "to": "scripts/", "filter": ["**/*"]}], "win": {"target": {"target": "nsis", "arch": ["x64"]}, "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Telefoncu Ta<PERSON>mi"}}}